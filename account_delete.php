<?php
session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
include('lib/auth.php');
include('admin/inc/resize-class.php');
$page = 0;
$sub_page = 0;
$profile = 8;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <title>Deva King 777</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <link rel="icon" href="xx.jpg">


  <!--Google font-->
  <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">


  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">



  <!--icon css-->
  <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
  <link rel="stylesheet" type="text/css" href="assets/css/themify.css">

  <!--Slick slider css-->
  <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
  <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">


  <!--Animate css-->
  <link rel="stylesheet" type="text/css" href="assets/css/animate.css">

  <!-- Bootstrap css -->
  <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">



  <!-- Theme css -->

  <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
  <!-- custom responsive css -->
  <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">

</head>

<body class="bg-light ">



  <!-- loader start -->

  <div class="loader-wrapper">

    <div>

      <img src="assets/images/loader.gif" alt="loader">

    </div>

  </div>

  <!-- loader end -->



  <!--header start-->

  <?php include('header.php') ?>

  <!--header end-->



  <!-- breadcrumb start -->

  <div class="breadcrumb-main ">

    <div class="container">

      <div class="row">

        <div class="col">

          <div class="breadcrumb-contain">

            <div>

              <h2>My-Account</h2>

              <ul>

                <li><a href="#">home</a></li>

                <li><i class="fa fa-angle-double-right"></i></li>

                <li><a href="#">My-Account</a></li>

              </ul>

            </div>

          </div>

        </div>

      </div>

    </div>

  </div>

  <!-- breadcrumb End -->



  <!-- section start -->

  <section class="section-big-py-space bg-light">

    <div class="container">

      <div class="row">

        <?php include('account_sidebar.php'); ?>

        <div class="col-lg-9">



          <div class="dashboard-right">

            <div class="dashboard" style="padding-top: 1px;">



              <div class="box-account box-info">

                <div class="box-head">

                  <h2>Delete Account</h2>
                </div>

                <div class="row mt-20">

                  <div class="col-sm-12">

                    <form class="theme-form" id="delete_account" method="post">
                      <div class="form-row">
                        <div class="col-md-6">
                          <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" name="password" class="form-control" id="password" placeholder="Enter Password" autocomplete="old-password" required>
                          </div>
                        </div>
                        <div class="col-md-7">
                          <button type="submit" id="submit" class="btn btn-normal pull-right">Delete Account</button>
                        </div>
                      </div>
                      <div class="loader_login"></div>
                    </form>


                  </div>



                </div>



              </div>

            </div>

          </div>

        </div>

      </div>

    </div>

  </section>

  <!-- section end -->







  <!--footer-start-->



  <?php include('footer.php') ?>

  <!--footer-end-->





  <!-- tap to top -->

  <div class="tap-top">

    <div>

      <i class="fa fa-angle-double-up"></i>

    </div>

  </div>

  <!-- tap to top End -->







  <!-- latest jquery-->

  <script src="assets/js/jquery-3.3.1.min.js"></script>



  <!-- slick js-->

  <script src="assets/js/slick.js"></script>



  <!-- popper js-->

  <script src="assets/js/popper.min.js"></script>



  <!-- Timer js-->

  <script src="assets/js/menu.js"></script>



  <!-- Bootstrap js-->

  <script src="assets/js/bootstrap.js"></script>



  <!-- Bootstrap js-->

  <script src="assets/js/bootstrap-notify.min.js"></script>

  <!-- Theme js-->

  <script src="assets/js/script.js"></script>
</body>

</html>

<script>
  var password = document.getElementById("password"),
    confirm_password = document.getElementById("confirm_password");

  function validatePassword() {
    if (password.value != confirm_password.value) {
      confirm_password.setCustomValidity("Passwords Don't Match");
    } else {
      confirm_password.setCustomValidity('');
    }
  }

  password.onchange = validatePassword;
  confirm_password.onkeyup = validatePassword;
</script>

<script>
  $("#delete_account").submit(function(e) {
    $("#submit").attr('disabled', true);
    $(".loader_login").html('<img src="loader.gif"> please wait...');
    $.ajax({
      url: 'ajax/delete_account.php',
      type: 'post',
      data: $("#delete_account").serialize(),
      success: function(data) {
        if (data == 1) {
          $("#submit").attr('disabled', false);
          $(".loader_login").html('<div class="alert alert-success" role="alert">Account deleted successfully!</div>');
          setTimeout(function() {
            window.location.href = 'logout.php'; // Redirect to logout or homepage after deletion
          }, 3000);
        } else {
          $("#submit").attr('disabled', false);
          $(".loader_login").html('<div class="alert alert-danger" role="alert">Password is incorrect!</div>');
          setTimeout(function() {
            $(".loader_login").html('');
          }, 8000);
        }
      }
    });
    e.preventDefault(); // Prevent default form submission
  });
</script>

<script>
  $("#change_password").submit(function(e) {
    $("#submit").attr('disabled', true);
    $(".loader_login").html('<img src="loader.gif"></i> please wait...');
    $.ajax({
      url: 'ajax/change_password.php',
      type: 'post',
      data: $("#change_password").serialize(),
      success: function(data) {
        if (data == 1) {
          $("#submit").attr('disabled', false);
          $(".loader_login").html('<div class="alert alert-success" role="alert">Password change successfully!</div>');
          setTimeout(function() {
            $(".loader_login").html('');
          }, 8000);
        } else {
          $("#submit").attr('disabled', false);
          $(".loader_login").html('<div class="alert alert-danger" role="alert">Old Password does not match!</div>');
          setTimeout(function() {
            $(".loader_login").html('');
          }, 8000);

        }
      },
    });
    e.preventDefault(); // avoid to execute the actual submit of the form.
  });
</script>
<style>
  .mt-20 {

    margin-top: 20px;

  }



  .btn-normal {

    font-size: 14px;

    padding: 15px 25px;

  }
</style>