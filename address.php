<?php
session_start();
include ('admin/lib/db_connection.php');
include ('admin/lib/get_functions.php');
include ('lib/auth.php');
include ('admin/inc/resize-class.php');
$page = 0;
$sub_page = 0;
$profile = 4;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">

<head>

	<title>Deva King 777</title>

	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

	<meta http-equiv="X-UA-Compatible" content="IE=edge">

	<meta name="viewport" content="width=device-width,initial-scale=1">
	<link rel="icon" href="./assets/img/logo/icon.png">





	<!--Google font-->

	<link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">

	<link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">





	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">





	<!--icon css-->

	<link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">

	<link rel="stylesheet" type="text/css" href="assets/css/themify.css">



	<!--Slick slider css-->

	<link rel="stylesheet" type="text/css" href="assets/css/slick.css">

	<link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">



	<!--Animate css-->

	<link rel="stylesheet" type="text/css" href="assets/css/animate.css">

	<!-- Bootstrap css -->

	<link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">



	<!-- Theme css -->

	<link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
	<!-- custom responsive css -->
	<link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">

</head>

<body class="bg-light ">



	<!-- loader start -->

	<div class="loader-wrapper">
		<div>
			<!-- <img src="assets/images/loader.gif" alt="loader"> -->
			<video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
		</div>
	</div>

	<!-- loader end -->



	<!--header start-->

	<?php include ('header.php') ?>

	<!--header end-->





	<!-- breadcrumb start -->

	<div class="breadcrumb-main ">

		<div class="container">

			<div class="row">

				<div class="col">

					<div class="breadcrumb-contain">

						<div>

							<h2>My-Account</h2>

							<ul>

								<li><a href="index.php">home</a></li>

								<li><i class="fa fa-angle-double-right"></i></li>

								<li><a href="address.php">Adress</a></li>

							</ul>

						</div>

					</div>

				</div>

			</div>

		</div>

	</div>

	<!-- breadcrumb End -->



	<!-- section start -->

	<section class="section-big-py-space bg-light">
		<div class="container">
			<div class="row">
				<?php include ('account_sidebar.php'); ?>
				<div class="col-lg-9">
					<div class="dashboard-right">
						<div class="dashboard" style="padding-top: 1px;">
							<div class="box-account box-info">
								<div class="box-head">
									<h2> Address</h2>
								</div>
								<div class="row mt-20">
									<div class="col-sm-12">
										<div class="row address-row">
											<div class="col-md-7">
												<?php $sel_address = dbQuery("SELECT * FROM tabl_customer WHERE id='" . $_SESSION['customer_id'] . "'");
												$res_customer = dbFetchAssoc($sel_address);

												$state = dbQuery("SELECT name FROM tabl_state WHERE id='" . $res_customer['state'] . "'");
												$res_state = dbFetchAssoc($state);

												?>
												<address>
													<?php echo $res_customer['address']; ?><br>
													<?php echo $res_customer['city']; ?> <br>
													<?php echo $res_state['name']; ?> <br>
													<?php echo $res_customer['pincode']; ?>
												</address>

											</div>
											<div class="col-md-2">
												<a href="javascript:void(0)" class="view hhh"><i
														class="fa fa-pencil edit-add" data-target="#edit-address"
														data-toggle="modal"> Edit</i> </a>
											</div>
											<div class="col-md-2">
												<!-- <ul>
													<li class="view hhh"><i class="fa fa-trash"> Delete</i> </li>
												</ul>-->
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- section end -->



	<!--add address-->

	<div id="edit-address" class="modal fade edit-address-box" role="dialog">
		<div class="modal-dialog md-modal">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title">
						<h3>Edit Address</h3>
					</h4>
					<button type="button" class="close" data-dismiss="modal">&times;</button>
				</div>

				<div class="modal-body">
					<form class="form" action="" method="post" id="addmyaddress">
						<div class="form-group">
							<div class="row">
								<div class="col-md-6">
									<label for="phone">
										<h4>Full Address</h4>
									</label>
									<input type="text" class="form-control" name="address" id="full_address"
										placeholder="Enter full address" value="<?php echo $res_customer['address']; ?>">
								</div>

								<div class="col-md-6">
									<label for="phone">
										<h4>Enter City</h4>
									</label>
									<input type="text" class="form-control" name="city" id="city"
										placeholder="Enter full city" value="<?php echo $res_customer['city']; ?>"
										required>
								</div>
							</div>
						</div>


						<div class="form-group">
							<div class="row">
								<div class="col-md-6">
									<label for="phone">
										<h4>State</h4>
									</label>
									<select id="state" class="form-control" name="state" required>
										<option value="">SELECT</option>
										<?php $sel_state = dbQuery("SELECT * FROM tabl_state ORDER BY name ASC");
										while ($res_state = dbFetchAssoc($sel_state)) {
											if ($res_state['id'] == $res_customer['state']) {
												$selected = 'selected';
											} else {
												$selected = '';
											}
											?>
											<option value="<?php echo $res_state['id']; ?>" <?php echo $selected; ?>>
												<?php echo $res_state['name']; ?></option>
										<?php } ?>
									</select>

								</div>


								<div class="col-md-6">
									<label for="phone">
										<h4>Enter PinCode</h4>
									</label>
									<input type="text" class="form-control" name="pincode" id="pincode"
										placeholder="Enter your pin" value="<?php echo $res_customer['pincode']; ?>"
										onkeypress="return isNumber(event,this)" required>
								</div>

							</div>

						</div>



						<div class="form-group">

							<div class="row">

								<div class="col-md-12">

									<br>

									<button class="btn btn-md btn-blue" id="submit" type="submit"><i
											class="glyphicon glyphicon-plus"></i> Edit Address</button>

								</div>

							</div>

						</div>
						<p class="profile_loader"></p>

					</form>

				</div>

			</div>

		</div>

	</div>




	<!--footer-start-->



	<?php include ('footer.php') ?>

	<!--footer-end-->





	<!-- tap to top -->

	<div class="tap-top">

		<div>

			<i class="fa fa-angle-double-up"></i>

		</div>

	</div>

	<!-- tap to top End -->







	<!-- latest jquery-->

	<script src="assets/js/jquery-3.3.1.min.js"></script>



	<!-- slick js-->

	<script src="assets/js/slick.js"></script>



	<!-- popper js-->

	<script src="assets/js/popper.min.js"></script>



	<!-- Timer js-->

	<script src="assets/js/menu.js"></script>



	<!-- Bootstrap js-->

	<script src="assets/js/bootstrap.js"></script>



	<!-- Bootstrap js-->

	<script src="assets/js/bootstrap-notify.min.js"></script>



	<!-- Theme js-->

	<script src="assets/js/script.js"></script>

	<script src="assets/js/slider-animat.js"></script>

	<script src="assets/js/timer.js"></script>

	<script src="assets/js/modal.js"></script>

</body>

<style>
	.mt-20 {

		margin-top: 20px;

	}



	.btn-normal {

		font-size: 14px;

		padding: 15px 25px;

	}
</style>

</html>

<script>
	function isNumber(evt) {
		var iKeyCode = (evt.which) ? evt.which : evt.keyCode
		if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
			return false;

		return true;
	}

	function isDecimal(evt, obj) {

		var charCode = (evt.which) ? evt.which : event.keyCode
		var value = obj.value;
		var dotcontains = value.indexOf(".") != -1;
		if (dotcontains)
			if (charCode == 46) return false;
		if (charCode == 46) return true;
		if (charCode > 31 && (charCode < 48 || charCode > 57))
			return false;
		return true;
	}


</script>
<script>
	$("#addmyaddress").submit(function (e) {
		$("#submit").attr('disabled', true);
		$(".profile_loader").html('<div class="alert alert-success" role="alert"><img src="loader.gif"></i> please wait...</div>');
		$.ajax({
			url: 'ajax/update_address.php',
			type: 'post',
			data: $("#addmyaddress").serialize(),
			success: function (data) {
				if (data == 1) {
					$("#submit").attr('disabled', false);
					$(".profile_loader").html('<div class="alert alert-success" role="alert">Address updated successfully!</div>');
					setTimeout(function () {
						location.reload();
					}, 1000);
				}
				else {
					$("#submit").attr('disabled', false);
					$(".profile_loader").html('<div class="alert alert-danger" role="alert">something is wrong!</div>');
					setTimeout(function () {
						location.reload();
					}, 1000);

				}

			},
		});
		e.preventDefault(); // avoid to execute the actual submit of the form.
	});

</script>