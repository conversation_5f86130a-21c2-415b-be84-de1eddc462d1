<?php session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');?>
<!DOCTYPE html>
<html lang="en">
<head>
   <title>Deva King 777</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width,initial-scale=1">
<!-- <link rel="stylesheet" href="xx.jpg"> -->
<link rel="icon" href="./assets/img/logo/icon.png">
<!--Google font-->
<link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<!--icon css-->
<link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
<link rel="stylesheet" type="text/css" href="assets/css/themify.css">
<!--Slick slider css-->
<link rel="stylesheet" type="text/css" href="assets/css/slick.css">
<link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">
<!--Animate css-->
<link rel="stylesheet" type="text/css" href="assets/css/animate.css">
<!-- Bootstrap css -->
<link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">
<!-- Theme css -->
<link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
<!-- custom responsive css -->
<link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">
</head>
<body class="bg-light ">
<!-- loader start -->
<div class="loader-wrapper">
        <div>
            <!-- <img src="assets/images/loader.gif" alt="loader"> -->
            <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
        </div>
    </div>
<!-- loader end --><!--header start-->
<?php include('header.php')?>
<!--header end--><!-- section start -->

<style>
    a.btn.btn-rounded {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding: 10px 0px;
    margin: 2px;
}
    .btn-rounded{
        background-color: #832729 !important;
    }
    @media screen and(max-width : 767px){
        
        .btn-rounded{
            background-color: #832729 !important;
            font-size: 13px;
        }
        .btn-none-2{
            display: none;
        }
        .product-box{
        width: 50% !important;
    }
    .product .product-box {
     box-shadow: none !important;
}
a.btn.btn-rounded {
    width: 70%;
    margin-left: auto;
    margin-right: auto;
    padding: 10px 0px;
}
    }
    .product-box{
        width: 100% !important;
    }
    .custum-col .col{
        padding-left: 0 !important;
        padding-right: 5 !important;
    }
</style>

<section>
        <!-- <div class=" custom-container">
            <div class="row">
                <div class="col banner_promt collection-banner ">
                    <a href="#" class="custom-container-2">
                      <img src="./assets/images/static-banner/ring.png" class="img-fluid" alt="">
                      <h6 class="view-all">View All</h6>
                    </a>
                </div>
                <div class="col d-lg-block  d-none">
                <a href="#" class="custom-container-2">
                <img src="./assets/images/static-banner/earing.png" class="img-fluid" alt="">
                <h6 class="view-all">View All</h6>
                </a>
                </div>
            </div>
        </div> -->
    </section>

<!-- <div class="product-banner custom-container mt-4">
    <img src="./assets/images/static-banner/Rudra1.png" class="img-fluid" alt="">
</div> -->

<!-- All products -->
<section>
<div class="title1 section-my-space">
        <h4 class="text-center">All Products</h4>
    </div>
</section>
<!-- All products -->
<!-- <style>
    @media only screen and (max-width: 600px) {
  .custom-container {
    margin-left: 10px;
  }
}
</style> -->
<section class="product section-pb-space ">
        <div class="custom-container " >
            <div class="">
                <div class="d-flex justify-content-center">
                    <div class="row custum-col">
                        <?php $special = dbQuery("SELECT * FROM tabl_products WHERE status='1'");
                        while ($res_special = dbFetchAssoc($special)) {
                            $special_off = (($res_special['old_price'] - $res_special['price']) / $res_special['old_price']) * 100;
                            $special_off = round($special_off, 0);
                        ?>
                            <div class="col col-lg-3 col-md-6   d-md-flex  justify-content-center">
                                <div class="product-box" >
                                    <div class="product-imgbox">
                                        <a href="product-detail.php?productID=<?php echo $res_special['id']; ?>">
                                            <div class="product-front">
                                                <img src="assets/images/products/<?php echo $res_special['image']; ?>" class="img-fluid" alt="product">
                                            </div>
                                        </a>
                                        <div class="new-label">
                                            <div><?php echo $special_off; ?>%</div>
                                        </div>
                                    </div>
                                    <div class=" detail-center detail-inverse p-2">
                                        <div class="detail-title">
                                           <div>
                                             <div class="detail-left">

                                                <a href="#">
                                                    <h6 class="price-title text-center">
                                                        <?php echo $res_special['name'] ?>
                                                    </h6>
                                                </a>
                                            </div>
                                            <div class="detail-right">
                                                <del><div class="check-price text-danger text-center">
                                                    Rs. <?php echo $res_special['old_price'] ?>
                                                </div></del>
                                                <div class="price">
                                                    <div class="price text-center">
                                                        Rs. <?php echo $res_special['price'] ?>
                                                    </div>
                                                </div>
                                            </div>
                                           </div>
                                           <div class="d-flex">
                                           <a href="product-detail.php?productID=<?php echo $res_special['id']; ?>" class="btn btn-rounded mtb-20 d-block">Add To Cart </a>
                                            <a href="product-detail.php?productID=<?php echo $res_special['id']; ?>" class="btn btn-rounded btn-none-2 d-none d-lg-block mtb-20">Buy Now </a>
                                           </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>

                    </div>
                </div>
            </div>
        </div>
    </section>



<!-- section End --><!--footer-start-->
<?php include('footer.php')?>
<!--footer-end--><!-- tap to top -->
<div class="tap-top">
  <div> <i class="fa fa-angle-double-up"></i> </div>
</div>
<!-- tap to top End --><!-- latest jquery--><script src="assets/js/jquery-3.3.1.min.js" ></script><!-- menu js--><script src="assets/js/menu.js"></script><!-- popper js--><script src="assets/js/popper.min.js" ></script><!-- slick js--><script  src="assets/js/slick.js"></script><!-- Bootstrap js--><script src="assets/js/bootstrap.js" ></script><!-- Theme js--><script src="assets/js/script.js" ></script>
</body>

<script>$(document).ready(function(){  $(".f_sort").click(function(){
		     var f_sort ='';$("input[name='f_sort']:checked");
			 var sor_par=$(this).val();
			    var url=$(location).attr('href');
				var urlParams = new URLSearchParams(window.location.search); 
				//get all parametersvar
			 is_param= urlParams.get('f_sort'); 
				if(is_param) {var url = new URL(url);var search_params = url.searchParams;search_params.set('f_sort', $(this).val());url.search = search_params.toString();var url = url.toString();var url=decodeURI(url);console.log(url);  }else{	var url=''+url+'&f_sort='+$(this).val()+'';   }         window.history.pushState('page2', 'Title', url);    var get_url=url.split('?');    $.ajax({	  url:'ajax/get_filter_page.php?'+get_url[1]+'',	  type:'get',	  beforeSend: function(){     $(".product-wrapper-grid").html('<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>');   },	  success:function(data){		   $(".product-wrapper-grid").html(data);		   $('html, body').animate({        scrollTop: $(".product-wrapper-grid").offset().top    }, 500);	   },	  });    }); });</script>
<script>$(document).ready(function(){  $(".sort_color").click(function(){	     var color_code = [];$.each($("input[name='color']:checked"), function(){                color_code.push($(this).val());            });if($(this).prop("checked") == true){			   var url=$(location).attr('href');   var new_url=''+url+'&color[]='+$(this).val()+'';      window.history.pushState('page2', 'Title', new_url);    var get_url=new_url.split('?');}else{	 var url=$(location).attr('href');  var avoid = '&color[]='+$(this).val()+''; var url=url.replace(avoid, ''); window.history.pushState('page2', 'Title', ''+url+''); var get_url=url.split('?');}    $.ajax({	  url:'ajax/get_filter_page.php?'+get_url[1]+'',	  type:'get',	 beforeSend: function(){     $(".product-wrapper-grid").html('<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>');   },	  success:function(data){		   $(".product-wrapper-grid").html(data);		   $('html, body').animate({        scrollTop: $(".product-wrapper-grid").offset().top    }, 500);	   },	  });    }); });</script><script>$(document).ready(function(){  $(".sort_size").click(function(){	     var size_code = [];$.each($("input[name='size']:checked"), function(){                size_code.push($(this).val());            });if($(this).prop("checked") == true){			   var url=$(location).attr('href');   var new_url=''+url+'&size[]='+$(this).val()+'';    window.history.pushState('page2', 'Title', new_url);    var get_url=new_url.split('?');}else{	 var url=$(location).attr('href');  var avoid = '&size[]='+$(this).val()+''; var url=url.replace(avoid, ''); window.history.pushState('page2', 'Title', ''+url+''); var get_url=url.split('?');}    $.ajax({	  url:'ajax/get_filter_page.php?'+get_url[1]+'',	  type:'get',	beforeSend: function(){     $(".product-wrapper-grid").html('<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>');   },	  success:function(data){		   $(".product-wrapper-grid").html(data);		   $('html, body').animate({        scrollTop: $(".product-wrapper-grid").offset().top    }, 500);	   },	  });    }); });</script><script>$(document).ready(function(){  $(".sort_av").click(function(){	     var av_code = [];$.each($("input[name='availablity']:checked"), function(){                av_code.push($(this).val());            });if($(this).prop("checked") == true){			   var url=$(location).attr('href');   var new_url=''+url+'&availablity[]='+$(this).val()+'';    window.history.pushState('page2', 'Title', new_url);    var get_url=new_url.split('?');}else{	 var url=$(location).attr('href');  var avoid = '&availablity[]='+$(this).val()+''; var url=url.replace(avoid, ''); window.history.pushState('page2', 'Title', ''+url+''); var get_url=url.split('?');}    $.ajax({	  url:'ajax/get_filter_page.php?'+get_url[1]+'',	  type:'get',	beforeSend: function(){     $(".product-wrapper-grid").html('<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>');   },	  success:function(data){		   $(".product-wrapper-grid").html(data);		   $('html, body').animate({        scrollTop: $(".product-wrapper-grid").offset().top    }, 500);	   },	  });    }); });</script><script>$(document).ready(function(){  $(".sort_price").click(function(){	     var price_code = [];$.each($("input[name='price']:checked"), function(){                price_code.push($(this).val());            });if($(this).prop("checked") == true){			   var url=$(location).attr('href');   var new_url=''+url+'&price[]='+$(this).val()+'';    window.history.pushState('page2', 'Title', new_url);    var get_url=new_url.split('?');}else{	 var url=$(location).attr('href');  var avoid = '&price[]='+$(this).val()+''; var url=url.replace(avoid, ''); window.history.pushState('page2', 'Title', ''+url+''); var get_url=url.split('?');}    $.ajax({	  url:'ajax/get_filter_page.php?'+get_url[1]+'',	  type:'get',	 beforeSend: function(){     $(".product-wrapper-grid").html('<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>');   },	  success:function(data){		   $(".product-wrapper-grid").html(data);	$('html, body').animate({        scrollTop: $(".product-wrapper-grid").offset().top    }, 500);  	   },	  });    }); });</script>
</html>