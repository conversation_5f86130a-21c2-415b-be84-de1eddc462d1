<?php
session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
//include('auth.php');
$page = 1;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>Deva King 777</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">

    <!-- <link rel="stylesheet" href="xx.jpg"> -->
    <link rel="icon" href="./assets/img/logo/icon.png">


    <!--Google font-->
    <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">


    <!--icon css-->
    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
    <link rel="stylesheet" type="text/css" href="assets/css/themify.css">

    <!--Slick slider css-->
    <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
    <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">

    <!--Animate css-->
    <link rel="stylesheet" type="text/css" href="assets/css/animate.css">
    <!-- Bootstrap css -->
    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">

    <!-- Theme css -->
    <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
    <!-- custom responsive css -->
    <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">
</head>

<body class="bg-light ">

    <!-- loader start -->
    <!-- <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video> -->
    <!-- <div class="loader-wrapper">
        <div>
            <img src="assets/images/loader.gif" alt="loader">
        </div>
    </div> -->
    <!-- loader end -->

    <!--header start-->
    <?php include('header.php') ?>
    <!--header end-->

    <!-- breadcrumb start -->
    <div class="breadcrumb-main ">
        <div class="container">
            <div class="row">
                <div class="col">
                    <div class="breadcrumb-contain">
                        <div>
                            <h2>cart</h2>
                            <ul>
                                <li><a href="#">home</a></li>
                                <li><i class="fa fa-angle-right"></i></li>
                                <li><a href="#">cart</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- breadcrumb End -->

    <!--section start-->
    <section class="cart-section section-big-py-space bg-light">
        <div class="custom-container">
            <div class="row">
                <div class="col-sm-12">
                    <table class="table cart-table table-responsive-xs">
                        <thead>
                            <tr class="table-head">
                                <th scope="col">image</th>
                                <th scope="col">product name</th>
                                <th scope="col">price</th>
                                <th scope="col">quantity</th>
                                <th scope="col">action</th>
                                <th scope="col">total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $sel_cart = dbQuery("SELECT tabl_products.*,tabl_cart.price as cart_price,tabl_cart.* FROM tabl_products INNER JOIN tabl_cart ON tabl_products.id=tabl_cart.product_id WHERE session_id='" . $session_id . "'");
                            $shipping_total = 0;
                            $total = 0;
                            $grand_total = 0;
                            $cart_num = dbNumRows($sel_cart);
                            if ($cart_num > 0) {
                                $i = 1;
                                while ($res_cart = dbFetchAssoc($sel_cart)) {

                                    $color = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_cart['color'] . "'");
                                    $color_num = dbNumRows($color);
                                    $res_color = dbFetchAssoc($color);

                                    $size = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_cart['size'] . "'");
                                    $size_num = dbNumRows($size);
                                    $res_size = dbFetchAssoc($size);

                                    $shipping_total += $res_cart['shipping_price'];
                                    $total += $res_cart['total'];
                            ?>
                                    <tr>
                                        <td>
                                            <a href="#"><img src="assets/images/products/<?php echo $res_cart['image'] ?>" alt="cart" class=""></a>
                                        </td>
                                        <td><a href="#"><?php echo $res_cart['name']; ?></a><br />
                                            <?php if ($color_num > 0) { ?>
                                                <span class="option">Color: <span class="color"><?php echo $res_color['v_value']; ?></span></span>
                                            <?php }
                                            if ($size_num > 0) { ?>
                                                <span class="option">Size: <span class="size"><?php echo $res_size['v_value']; ?></span></span>
                                            <?php } ?>
                                            <div class="mobile-cart-content row">
                                                <div class="col-xs-3">
                                                    <div class="qty-box">
                                                        <div class="input-group">
                                                            <span class="input-group-btn">
                                                                <button type="button" class="btn btn-default btn-number cart_button_<?php echo $i; ?>" style="height: 38px;" onClick="update_cart_qty(<?php echo '2,' . $i . ',' . $res_cart['id'] ?>);">
                                                                    <span class="fa fa-minus"></span>
                                                                </button>
                                                            </span>
                                                            <input type="text" name="qty_<?php echo $i; ?>" class="form-control input-number" value="<?php echo $res_cart['qty']; ?>" id="qty_<?php echo $i; ?>" min="1" max="10" readonly>
                                                            <span class="input-group-btn">
                                                                <button type="button" class="btn btn-default btn-number cart_button_<?php echo $i; ?>" style="height: 38px;" onClick="update_cart_qty(<?php echo '1,' . $i . ',' . $res_cart['id'] ?>);">
                                                                    <span class="fa fa-plus"></span>
                                                                </button>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-xs-3">
                                                    <h2 class="td-color">Rs <?php echo $res_cart['total']; ?></h2>
                                                </div>
                                                <div class="col-xs-3">
                                                    <h2 class="td-color"><a href="javascript:void(0)" onclick="remove_cart(<?php echo $res_cart['id']; ?>)" class="icon icon-remove"><i class="fa fa-close"></i></a></h2>
                                                    <div class="remove_cart"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <h2>Rs <?php echo $res_cart['cart_price']; ?></h2>
                                        </td>
                                        <td>
                                            <div class="qty-box">

                                                <div class="input-group">
                                                    <span class="input-group-btn">
                                                        <button type="button" class="btn btn-default btn-number cart_button_<?php echo $i; ?>" style="height: 38px;" onClick="update_cart_qty(<?php echo '2,' . $i . ',' . $res_cart['id'] ?>);">
                                                            <span class="fa fa-minus"></span>
                                                        </button>
                                                    </span>
                                                    <input type="text" name="qty_<?php echo $i; ?>" class="form-control input-number" value="<?php echo $res_cart['qty']; ?>" id="qty_<?php echo $i; ?>" min="1" max="10" readonly>
                                                    <span class="input-group-btn">
                                                        <button type="button" class="btn btn-default btn-number cart_button_<?php echo $i; ?>" style="height: 38px;" onClick="update_cart_qty(<?php echo '1,' . $i . ',' . $res_cart['id'] ?>);">
                                                            <span class="fa fa-plus"></span>
                                                        </button>
                                                    </span>
                                                </div>
                                                <div class="qty_loader_<?php echo $i; ?>"></div>
                                            </div>
                                        </td>
                                        <td><a href="javascript:void(0)" onclick="remove_cart(<?php echo $res_cart['id']; ?>)" class="icon icon-remove"><i class="fa fa-close"></i></a>
                                            <div class="remove_cart"></div>
                                        </td>
                                        <td>
                                            <h2 class="td-color">Rs <?php echo  $res_cart['total']; ?></h2>
                                        </td>
                                    </tr>
                            <?php $i++;
                                }
                                $grand_total = $total + $shipping_total;
                            } else {
                                echo '<tr><td colspan="6">Shopping cart is empty!</td></tr>';
                            } ?>
                        </tbody>

                    </table>
                    <?php if ($cart_num > 0) { ?>
                        <table class="table cart-table table-responsive-md">
                            <tfoot>
                                <tr>
                                    <td>Total Order Value :</td>
                                    <td>
                                        <h2>Rs <?php echo $total; ?></h2>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Total Shipping Charges :</td>
                                    <td>
                                        <h2>Rs <?php echo $shipping_total; ?></h2>
                                    </td>
                                </tr>


                                <tr>
                                    <td>
                                        <form>
                                            <div class="row">
                                                <div class="form-group col-md-12 col-sm-12 col-xs-12">
                                                    <label>Coupon Code:</label>
                                                    <!-- <input type="text" name="field-name" value="" placeholder="">-->
                                                </div>
                                            </div>
                                        </form>
                                    </td>

                                    <td>
                                        <a href="offer.php" class="btn btn-rounded" data-toggle="modal" data-target="#quick-view">Apply Coupon</a>
                                    </td>
                                </tr>

                                <?php
                                $sel1 = dbQuery("SELECT promo_Code FROM `tabl_cart_promo` WHERE session_id='" . $session_id . "'");
                                $offer_num = dbNumRows($sel1);
                                if ($offer_num > 0) {
                                ?>
                                    <tr>
                                        <td>
                                            <div class="head_div1">
                                                <ul>
                                                    <?php
                                                    while ($res1 = dbFetchAssoc($sel1)) {
                                                        $sel_offer = dbQuery("SELECT * FROM `tabl_promo_code` WHERE id='" . $res1['promo_Code'] . "'");
                                                        $res_offer = dbFetchAssoc($sel_offer);
                                                    ?>
                                                        <li class="pdp_offrs mtb-20" id="first_promo">
                                                            <div class="coupons_code">
                                                                <span><?php echo $res_offer['name']; ?></span>

                                                            </div>
                                                        </li>
                                                    <?php } ?>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php } ?>
                                <tr>
                                    <td>Total Discount :</td>
                                    <td>
                                        <?php $promo = dbQuery("SELECT sum(discount_price) as ttl_discount FROM tabl_cart_promo WHERE session_id='" . $session_id . "'");
                                        $res_promo = dbFetchAssoc($promo);
                                        if ($res_promo['ttl_discount'] == "") {
                                            $discount = 0;
                                        } else {
                                            $discount = $res_promo['ttl_discount'];
                                        }
                                        ?>
                                        <h2 class="discount">- Rs <?php echo $discount; ?></h2>
                                    </td>
                                </tr>

                                <tr>
                                    <td>Grand Total :</td>
                                    <td>
                                        <h2 class="grand_total">Rs <?php echo $grand_total - $discount; ?></h2>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    <?php } ?>
                </div>
            </div>
            <div class="row cart-buttons">
                <div class="col-12">
                    <a href="index.php" class="btn btn-normal">continue shopping</a>
                    <a href="checkout.php" class="btn btn-normal ml-3">check out</a>
                </div>
            </div>
        </div>
    </section>
    <!--section end-->

    <!--footer-start-->

    <?php include('footer.php') ?>
    <!--footer-end-->
    <!-- Quick-view modal popup start-->
    <div class="modal fade bd-example-modal-lg theme-modal" id="quick-view" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content quick-view-modal">
                <div class="modal-body">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <div class="row">

                        <div class="col-lg-12">
                            <?php
                            $sel_cat = dbQuery("SELECT sub_cat_id FROM tabl_cart WHERE session_id='" . $session_id . "' Group BY sub_cat_id");
                            $res_cat = dbFetchAssoc($sel_cat);

                            $sel_offer = dbQuery("SELECT * FROM tabl_promo_code WHERE start_date<='" . $date . "' AND end_date>='" . $date . "' AND FIND_IN_SET(" . $res_cat['sub_cat_id'] . ",sub_cat_id)");
                            $offer_num = dbNumRows($sel_offer);
                            if ($offer_num > 0) {

                            ?>
                                <div class="product-right">
                                    <form name="promo-form" id="promo-form" method="post">
                                        <div class="theme-card">
                                            <h3 class="text-center">Available Coupons</h3>
                                            <div class="offers_pdp">
                                                <div class="head_div">
                                                    <ul>
                                                        <?php while ($res_offer = dbFetchAssoc($sel_offer)) {
                                                            if ($res_offer['minimum_price'] <= $total) {

                                                                $sel1 = dbQuery("SELECT promo_Code FROM `tabl_cart_promo` WHERE session_id='" . $session_id . "' AND promo_code='" . $res_offer['id'] . "'");
                                                                $res1 = dbFetchAssoc($sel1);
                                                                if ($res_offer['id'] == $res1['promo_Code']) {
                                                                    $checked = 'checked';
                                                                } else {
                                                                    $checked = '';
                                                                }
                                                        ?>
                                                                <li class="pdp_offrs mtb-20" id="first_promo">
                                                                    <input type="checkbox" name="promo_code[]" value="<?php echo $res_offer['id']; ?>" class="mr-10" <?php echo $checked; ?>>
                                                                    <div class="coupons_code">
                                                                        <span><?php echo $res_offer['name']; ?></span>
                                                                    </div>

                                                                    <div>
                                                                        <p><?php echo $res_offer['description']; ?></p>
                                                                        <span><a href="term.php">T&amp;C</a>
                                                                        </span>
                                                                    </div>
                                                                </li>
                                                        <?php }
                                                        } ?>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="product-buttons text-center">
                                            <div class="promo_loader" style="text-align: center;text-transform: none;"></div>
                                            <button type="submit" id="submit" class="btn btn-normal">Apply Coupon</button>
                                        </div>

                                    </form>
                                </div>
                            <?php } else { ?>
                                <div class="product-right">
                                    <div class="theme-card">
                                        <h4 class="text-center">No Coupon Available!</h4>
                                    </div>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Quick-view modal popup end-->




    <!-- tap to top -->
    <div class="tap-top">
        <div>
            <i class="fa fa-angle-double-up"></i>
        </div>
    </div>
    <!-- tap to top End -->


    <!-- latest jquery-->
    <script src="assets/js/jquery-3.3.1.min.js"></script>

    <!-- menu js-->
    <script src="assets/js/menu.js"></script>

    <!-- popper js-->
    <script src="assets/js/popper.min.js"></script>

    <!-- slick js-->
    <script src="assets/js/slick.js"></script>

    <!-- Bootstrap js-->
    <script src="assets/js/bootstrap.js"></script>

    <!-- Theme js-->
    <script src="assets/js/script.js"></script>

</body>
<style>
    .product .product-box:hover .product-detail.detail-center .detail-title {
        opacity: 1;
    }

    .section-big-pt-space {
        padding-top: 0px;
    }

    .mt-30 {
        margin-top: 30px;
    }

    .mb-30 {
        margin-bottom: 30px;
    }

    .mt-50 {
        margin-top: 50px;
    }

    .mtb-20 {
        margin-top: 20px;
        margin-bottom: 20px;
    }


    .btn-rounded {
        font-size: 13px;
        padding: 10px 11px;
        font-weight: 700;
        color: #fff !important;
        /* background-color: #cabdb4;*/
        background-color: #ff708a;
        border-radius: 25px;
        position: relative;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
        line-height: 1;
        display: inline-block;
        letter-spacing: 0.05em;
    }

    .btn-rounded:hover {
        background-color: #ff708a;
    }

    .instagram-box img {
        max-width: 145px;
        height: 145px;
    }

    .product .product-box {
        margin-right: 15px;
        margin-top: 15px;
        padding-right: 0px;
    }

    .product .product-box .product-imgbox img {
        margin: 0 auto;
        max-width: 100%;
        height: auto;
    }

    .cart-section tfoot tr td,
    .wishlist-section tfoot tr td {
        padding-top: 10px;
        padding-bottom: 0;
        text-align: right;
        border: none;
        padding-right: 63px;
    }

    .offers_pdp ul li .coupons_code span {
        display: block;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        padding: 3px 8px;
        background: rgba(36, 163, 181, 0.1);
        border: 1px dashed #24a3b5;
        color: #212121;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
        -moz-background-clip: padding;
        -webkit-background-clip: padding-box;
        background-clip: padding-box;
        margin-right: 15px;
    }

    .offers_pdp ul li {
        display: flex;
        align-items: center;
        margin-top: 12px;
    }

    .theme-form {
        padding: 30px;
        background-color: #f1f4f7;
    }


    .offers_pdp {
        height: 230px;
        overflow-y: scroll;
    }

    .mr-10 {
        margin-right: 10px !important;
    }

    .head_div1 ul li .coupons_code span {
        display: block;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        padding: 3px 8px;
        background: rgba(36, 163, 181, 0.1);
        border: 1px dashed #24a3b5;
        color: #212121;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
        -moz-background-clip: padding;
        -webkit-background-clip: padding-box;
        background-clip: padding-box;
        margin-right: 15px;
    }

    .option {
        font-weight: bold;
    }

    .color {
        color: #e64040;
    }

    .size {
        color: #e64040;
    }
</style>

</html>
<script>
    $("#promo-form").submit(function(e) {
        var values = [];
        $("input[name='promo_code[]']:checked").each(function() {
            values.push($(this).val());
        });
        if (values != "") {
            $("#submit").attr('disabled', true);
            $(".promo_loader").html('<img src="loader.gif" style="width: 20px;"></i> please wait while we apply coupon code...');

            $.ajax({
                url: 'ajax/apply_promo_codes.php',
                type: 'post',
                data: {
                    'values': values
                },
                success: function(data) {
                    if (data == 1) {
                        $("#submit").attr('disabled', false);
                        $(".promo_loader").html('<span style="color: #59a759;font-weight: 500;"><i class="fa fa-check" style="color: #59a759;"></i> Promo code apply successfully!</span>');
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        $("#submit").attr('disabled', false);
                        $(".promo_loader").html('<span style="color: #e44d14;font-weight: 500;"><i class="fa fa-trash-o" style="color: #e44d14;"></i> something is wrong!</span>');
                    }
                },
            });
        } else {
            alert("Please select promo code!!!");
        }
        //$("#submit").attr('disabled',true);
        e.preventDefault(); // avoid to execute the actual submit of the form.
    });
</script>
<script>
    function remove_cart(cart_id) {
        $(".remove_cart").html('<img src="loader.gif" style="width: 10px;"></i> please wait...');
        $(".icon-remove").html('');
        $.ajax({
            url: 'ajax/remove_cart.php',
            type: 'post',
            data: {
                'cart_id': cart_id
            },
            success: function(data) {
                if (data == 1) {
                    location.reload();
                }
            },
        });
    }
</script>
<script>
    function update_cart_qty(type, row_id, cart_id) {
        if (type == 1) {
            var qty = $("#qty_" + row_id).val();
            var new_qty = parseInt(qty) + 1;
            $("#qty_" + row_id).val(new_qty);
        } else {
            var qty = $("#qty_" + row_id).val();
            if (qty != 1) {
                var new_qty = parseInt(qty) - 1;
                $("#qty_" + row_id).val(new_qty);
            }
        }
        $(".qty_loader_" + row_id).html('<img src="loader.gif" style="width: 10px;"></i> please wait...');
        $(".cart_button_" + row_id).attr('disabled', true);

        $.ajax({
            url: 'ajax/update_cart_qty.php',
            type: 'post',
            data: {
                'new_qty': new_qty,
                'cart_id': cart_id
            },
            success: function(data) {
                if (data == 1) {
                    location.reload();
                }
            },
        });

    }
</script>