<?php
session_start();
$session_id = session_id();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
//include('auth.php');
$page = 1;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
$cart_val = dbQuery("SELECT * FROM tabl_cart WHERE session_id='" . $session_id . "'");
$cart_num = dbNumRows($cart_val);
if ($cart_num == 0) {
    echo '<script>window.location.href="cart.php";</script>';
}

?>
<!DOCTYPE html>
<html lang="en">

<head>

    <title>Deva King 777</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="icon" href="./assets/img/logo/icon.png">
    <!--Google font-->
    <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <!--icon css-->
    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
    <link rel="stylesheet" type="text/css" href="assets/css/themify.css">
    <!--Slick slider css-->
    <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
    <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">
    <!--Animate css-->
    <link rel="stylesheet" type="text/css" href="assets/css/animate.css">
    <!-- Bootstrap css -->
    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">
    <!-- Theme css -->
    <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
    <!-- custom responsive css -->
    <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">
</head>

<body class="bg-light ">



    <!-- loader start -->

    <div class="loader-wrapper">
        <div>
            <!-- <img src="assets/images/loader.gif" alt="loader"> -->
            <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
        </div>
    </div>
    <!-- loader end -->



    <!--header start-->



    <?php include('header.php') ?>

    <!--header end-->



    <!-- breadcrumb start -->

    <div class="breadcrumb-main ">

        <div class="container">

            <div class="row">

                <div class="col">

                    <div class="breadcrumb-contain">

                        <div>

                            <h2>checkout</h2>

                            <ul>

                                <li><a href="#">home</a></li>

                                <li><i class="fa fa-angle-double-right"></i></li>

                                <li><a href="#">checkout</a></li>

                            </ul>

                        </div>

                    </div>

                </div>

            </div>

        </div>

    </div>

    <!-- breadcrumb End -->



    <!-- section start -->

    <section class="section-big-py-space bg-light">

        <div class="custom-container">

            <div class="checkout-page contact-page">

                <div class="checkout-form">
                    <?php if (@$_SESSION['customer_id'] == "") { ?>
                        <div class="row">
                            <div class="col-lg-6 col-sm-12 col-xs-12">
                                <form id="loginform" method="post">
                                    <div class="checkout-title" style="margin-left: 30px;">
                                        <h3>Login</h3>
                                    </div>
                                    <div class="theme-form">
                                        <div class="row check-out ">
                                            <div class="form-group col-md-12 col-sm-12 col-xs-12">
                                                <label>Email/Mobile</label>
                                                <input type="text" name="username" autocomplete="user-name" class="form-control" id="email" placeholder="Enter your mobile number or email id" required="">
                                            </div>
                                            <div class="form-group col-md-12 col-sm-12 col-xs-12">
                                                <label>Password</label>
                                                <input type="password" name="password" autocomplete="new-password" class="form-control" id="review" placeholder="Enter your password" required="">
                                            </div>
                                            <div class="form-group col-md-12 col-sm-12 col-xs-12">
                                                <button type="submit" class="btn-normal btn">Login</button>
                                                <a href="register.php" class="btn-normal btn">Register</a>
                                            </div>
                                        </div><br />
                                        <div class="loader_login"></div>
                                    </div>

                                </form>

                            </div>

                            <div class="col-lg-6 col-sm-12 col-xs-12">
                                <div class="checkout-details theme-form  section-big-mt-space">
                                    <div class="order-box">
                                        <div class="title-box">
                                            <div>Product <span>Total</span></div>
                                        </div>

                                        <ul class="qty">
                                            <?php $sel_cart = dbQuery("SELECT tabl_products.*,tabl_cart.price as cart_price,tabl_cart.* FROM tabl_products INNER JOIN tabl_cart ON tabl_products.id=tabl_cart.product_id WHERE session_id='" . $session_id . "'");
                                            $shipping_total = 0;
                                            $total = 0;
                                            $grand_total = 0;
                                            $i = 1;
                                            while ($res_cart = dbFetchAssoc($sel_cart)) {

                                                $color = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_cart['color'] . "'");
                                                $color_num = dbNumRows($color);
                                                $res_color = dbFetchAssoc($color);

                                                $size = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_cart['size'] . "'");
                                                $size_num = dbNumRows($size);
                                                $res_size = dbFetchAssoc($size);


                                                $promo = dbQuery("SELECT sum(discount_price) as ttl_discount FROM tabl_cart_promo WHERE session_id='" . $session_id . "'");
                                                $res_promo = dbFetchAssoc($promo);
                                                if ($res_promo['ttl_discount'] == "") {
                                                    $discount = 0;
                                                } else {
                                                    $discount = $res_promo['ttl_discount'];
                                                }

                                                $shipping_total += $res_cart['shipping_price'];
                                                $total += $res_cart['total'];
                                            ?>
                                                <li><?php echo $res_cart['name'] ?> × <?php echo $res_cart['qty']; ?> <span> Rs. <?php echo $res_cart['total']; ?></span><br />
                                                    <?php if ($color_num > 0) { ?>
                                                        <p class="option">Color: <?php echo $res_color['v_value']; ?></p>
                                                    <?php }
                                                    if ($size_num > 0) { ?>
                                                        <p class="option">Size: <?php echo $res_size['v_value']; ?></p>
                                                    <?php } ?>
                                                </li>
                                            <?php }
                                            $grand_total = $total + $shipping_total;
                                            ?>
                                        </ul>
                                        <ul class="sub-total">
                                            <li>Total Order Value <span class="count">Rs. <?php echo $total; ?></span></li>
                                            <li>Total shipping charges <span class="count">Rs.<?php echo $shipping_total; ?></span></li>
                                            <li>Total discount <span class="count">- Rs. <?php echo $discount; ?></span></li>
                                        </ul>

                                        <ul class="total">
                                            <li>Grand Total <span class="count">Rs. <?php echo $grand_total - $discount; ?></span></li>
                                        </ul>
                                    </div>

                                    <div class="payment-box">
                                        <div class="upper-box">
                                            <div class="payment-options">
                                                <ul>
                                                    <!-- <li>
                                                    <div class="radio-option">
                                                        <input type="radio" name="payment_signup" id="payment-signup-1" checked="checked" value="1">
                                                        <label for="payment-signup-1">Cash On Delivery</label>
                                                    </div>
                                                </li>
                                                <li>


                                                    <div class="radio-option">
                                                        <input type="radio" name="payment_signup" id="payment-signup-2" value="2">
                                                        <label for="payment-signup-2">Online Payments</label>
                                                    </div>
													                                                  
													
												</li> -->
                                                    <!--  <?php
                                                            $wallet = dbQuery("SELECT amount FROM tabl_wallet WHERE customer_id='" . $_SESSION['customer_id'] . "'");
                                                            $res_wallet = dbFetchAssoc($wallet);
                                                            if ($res_wallet['amount'] >= $grand_total - $discount) {
                                                            ?>
                                                 <li>
                                             
                                                    <div class="radio-option">
                                                        <input type="radio" name="payment" id="payment-3" value="3">
                                                        <label for="payment-3">Wallet</label>
                                                    </div>
													
												</li> 
                                                 <?php } ?>-->
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <button type="button" class="btn-normal btn" style="background: gray;" disabled>Proceed To Payment</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php } else { ?>
                        <form role="form" class="" name="checkout" id="checkout">
                            <div class="row">
                                <div class="col-lg-6 col-sm-12 col-xs-12">
                                    <div class="checkout-title" style="margin-left: 30px;">
                                        <h3>Shipping Details</h3>
                                    </div>
                                    <div class="theme-form">
                                        <?php
                                        $sel_customer = dbQuery("SELECT * FROM tabl_customer WHERE id='" . $_SESSION['customer_id'] . "'");
                                        $res_customer = dbFetchAssoc($sel_customer); ?>
                                        <div class="row check-out ">
                                            <div class="form-group col-md-12 col-sm-12 col-xs-12">
                                                <label>Name</label>
                                                <input type="text" name="order_name" name="name" value="<?php echo $res_customer['name'] ?>" required placeholder="">
                                            </div>
                                            <div class="form-group col-md-6 col-sm-6 col-xs-12">
                                                <label class="field-label">Phone</label>
                                                <input type="text" name="order_phone" value="<?php echo $res_customer['phone'] ?>" required placeholder="phone" onkeypress="return isNumber(event,this)">
                                            </div>

                                            <div class="form-group col-md-6 col-sm-6 col-xs-12">
                                                <label class="field-label">Email id</label>
                                                <input type="text" name="order_email" value="<?php echo $res_customer['email'] ?>" required placeholder="e-mail">
                                            </div>

                                            <div class="form-group col-md-12 col-sm-12 col-xs-12">
                                                <label class="field-label">Address</label>
                                                <input type="text" name="order_address" value="<?php echo $res_customer['address'] ?>" required placeholder="Street address">
                                            </div>

                                            <div class="form-group col-md-12 col-sm-12 col-xs-12">
                                                <label class="field-label">Town/City</label>
                                                <input type="text" name="order_city" value="<?php echo $res_customer['city'] ?>" required placeholder="City">
                                            </div>

                                            <div class="form-group col-md-12 col-sm-6 col-xs-12">
                                                <label class="field-label">State</label>
                                                <select id="id_states" name="order_state" required>
                                                    <option value="">SELECT</option>
                                                    <?php $sel_state = dbQuery("SELECT * FROM tabl_state ORDER BY name ASC");
                                                    while ($res_state = dbFetchAssoc($sel_state)) {
                                                        if ($res_state['id'] == $res_customer['state']) {
                                                            $selected = 'selected';
                                                        } else {
                                                            $selected = '';
                                                        }
                                                    ?>
                                                        <option value="<?php echo $res_state['id']; ?>" <?php echo $selected; ?>><?php echo $res_state['name']; ?></option>
                                                    <?php } ?>
                                                </select>
                                            </div>

                                            <div class="form-group col-md-12 col-sm-6 col-xs-12">
                                                <label class="field-label">Postal Code</label>
                                                <input type="text" name="order_postal_code" value="<?php echo $res_customer['pincode'] ?>" required placeholder="Postal Code" onkeypress="return isNumber(event,this)">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-6 col-sm-12 col-xs-12">
                                    <div class="checkout-details theme-form  section-big-mt-space">
                                        <div class="order-box">
                                            <div class="title-box">
                                                <div>Product <span>Total</span></div>
                                            </div>

                                            <ul class="qty">
                                                <?php $sel_cart = dbQuery("SELECT tabl_products.*,tabl_cart.price as cart_price,tabl_cart.* FROM tabl_products INNER JOIN tabl_cart ON tabl_products.id=tabl_cart.product_id WHERE session_id='" . $session_id . "'");
                                                $shipping_total = 0;
                                                $total = 0;
                                                $grand_total = 0;
                                                $i = 1;
                                                while ($res_cart = dbFetchAssoc($sel_cart)) {

                                                    $color = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_cart['color'] . "'");
                                                    $color_num = dbNumRows($color);
                                                    $res_color = dbFetchAssoc($color);

                                                    $size = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_cart['size'] . "'");
                                                    $size_num = dbNumRows($size);
                                                    $res_size = dbFetchAssoc($size);


                                                    $promo = dbQuery("SELECT sum(discount_price) as ttl_discount FROM tabl_cart_promo WHERE session_id='" . $session_id . "'");
                                                    $res_promo = dbFetchAssoc($promo);
                                                    if ($res_promo['ttl_discount'] == "") {
                                                        $discount = 0;
                                                    } else {
                                                        $discount = $res_promo['ttl_discount'];
                                                    }

                                                    $shipping_total += $res_cart['shipping_price'];
                                                    $total += $res_cart['total'];
                                                ?>
                                                    <li><?php echo $res_cart['name'] ?> × <?php echo $res_cart['qty']; ?> <span> Rs. <?php echo $res_cart['total']; ?></span><br />
                                                        <?php if ($color_num > 0) { ?>
                                                            <p class="option">Color: <?php echo $res_color['v_value']; ?></p>
                                                        <?php }
                                                        if ($size_num > 0) { ?>
                                                            <p class="option">Size: <?php echo $res_size['v_value']; ?></p>
                                                        <?php } ?>
                                                    </li>
                                                <?php }
                                                $grand_total = $total + $shipping_total;
                                                ?>
                                            </ul>
                                            <ul class="sub-total">
                                                <li>Total Order Value <span class="count">Rs. <?php echo $total; ?></span></li>
                                                <li>Total shipping charges <span class="count">Rs.<?php echo $shipping_total; ?></span></li>
                                                <li>Total discount <span class="count">- Rs. <?php echo $discount; ?></span></li>
                                            </ul>

                                            <ul class="total">
                                                <li>Grand Total <span class="count">Rs. <?php echo $grand_total - $discount; ?></span></li>
                                            </ul>
                                        </div>

                                        <div class="payment-box">
                                            <div class="upper-box">
                                                <div class="payment-options">
                                                    <ul>
                                                        <li>
                                                            <div class="radio-option">
                                                                <input type="radio" name="payment" id="payment-1" checked="checked" value="1">
                                                                <label for="payment-1">Cash On Delivery</label>
                                                            </div>
                                                        </li>
                                                        <li>

                                                            <div class="radio-option">
                                                                <input type="radio" name="payment" id="payment-2" value="2">
                                                                <label for="payment-2">Online Payments</label>
                                                            </div>

                                                        </li>
                                                        <!--   <?php
                                                                $wallet = dbQuery("SELECT amount FROM tabl_wallet WHERE customer_id='" . $_SESSION['customer_id'] . "'");
                                                                $res_wallet = dbFetchAssoc($wallet);
                                                                if ($res_wallet['amount'] >= $grand_total - $discount) {
                                                                ?>
                                                 <li>
                                             
                                                    <div class="radio-option">
                                                        <input type="radio" name="payment" id="payment-3" value="3">
                                                        <label for="payment-3">Wallet</label>
                                                    </div>
													
												</li> 
                                                 <?php } ?>-->
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <input type="hidden" value="<?php echo $grand_total - $discount; ?>" name="order_amount">
                                                <input type="hidden" value="<?php echo $shipping_total; ?>" name="order_shipping">

                                                <button type="submit" id="submit" class="btn-normal btn">Proceed To Payment</button>
                                            </div>
                                        </div>

                                        <div class="checkout_loader" style="width:100%;"></div>

                                    </div>
                                </div>
                            </div>
                        </form>
                    <?php } ?>
                </div>
            </div>
        </div>
    </section>

    <!-- section end -->





    <!--footer-start-->

    <?php include('footer.php') ?>

    <!--footer-end-->





    <!-- tap to top -->

    <div class="tap-top">

        <div>

            <i class="fa fa-angle-double-up"></i>

        </div>

    </div>

    <!-- tap to top End -->







    <!-- latest jquery-->

    <script src="assets/js/jquery-3.3.1.min.js"></script>



    <!-- slick js-->

    <script src="assets/js/slick.js"></script>



    <!-- popper js-->

    <script src="assets/js/popper.min.js"></script>



    <!-- Timer js-->

    <script src="assets/js/menu.js"></script>



    <!-- Bootstrap js-->

    <script src="assets/js/bootstrap.js"></script>



    <!-- Bootstrap js-->

    <script src="assets/js/bootstrap-notify.min.js"></script>



    <!-- Theme js-->

    <script src="assets/js/script.js"></script>

    <script src="assets/js/slider-animat.js"></script>

    <script src="assets/js/modal.js"></script>

</body>

</html>
<style>
    .option {
        font-size: 11px;
        font-weight: 600;
        color: #c51515;
    }
</style>
<script>
    $("#loginform").submit(function(e) {
        $("#submit").attr('disabled', true);
        $(".loader_login").html('<img src="loader.gif"></i> please wait...');
        $.ajax({
            url: 'ajax/customer_login.php',
            type: 'post',
            data: $("#loginform").serialize(),
            success: function(data) {
                if (data == 1) {
                    location.reload();
                } else {
                    $("#submit").attr('disabled', false);
                    $(".loader_login").html('<div class="alert alert-danger" role="alert">Either username or password are wrong!</div>');
                    setTimeout(function() {
                        $(".loader_login").html('');
                    }, 8000);

                }
            },
        });
        e.preventDefault(); // avoid to execute the actual submit of the form.
    });
</script>


<script>
    $("#checkout").submit(function(e) {
        $("#submit").attr('disabled', true);
        $(".checkout_loader").html('<div class="alert alert-success" role="alert"><img src="loader.gif"></i> please wait...</div>');
        $.ajax({
            url: 'ajax/checkout.php',
            type: 'post',
            data: $("#checkout").serialize(),
            success: function(data) {
                // window.location.href = 'payment_options.php';
                if (data == 1 || data == 3) {
                    window.location.href = 'thank-you.php';
                } else if (data == 2) {
                    // window.location.href = 'razorpay-php/pay.php';
                    window.location.href = 'phonepe_pay.php';
                } else {
                    $("#submit").attr('disabled', false);
                    $(".checkout_loader").html('<div class="alert alert-danger" role="alert">something is wrong!</div>');
                    setTimeout(function() {
                        $(".checkout_loader").html('');
                    }, 8000);

                }
            },
        });
        e.preventDefault(); // avoid to execute the actual submit of the form.
    });
</script>

<script>
    function isNumber(evt) {
        var iKeyCode = (evt.which) ? evt.which : evt.keyCode
        if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
            return false;

        return true;
    }

    function isDecimal(evt, obj) {

        var charCode = (evt.which) ? evt.which : event.keyCode
        var value = obj.value;
        var dotcontains = value.indexOf(".") != -1;
        if (dotcontains)
            if (charCode == 46) return false;
        if (charCode == 46) return true;
        if (charCode > 31 && (charCode < 48 || charCode > 57))
            return false;
        return true;
    }
</script>