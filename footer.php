<style>
    .footer-4 {
        background-color: #F2E9E9;
    }

    .footer-title h5 {
        color: #832729;
    }

    .footer-4 .footer-box .footer-contant ul li a {
        color: #832729;
        font-weight: 600;
    }

    .footer-4 .footer-box .footer-contant ul li {
        transition: 0.6s all ease-in-out;
        color: #832729;
        font-weight: 600;
    }

    .tap-top {
        background: #832729;
        box-shadow: rgb(0 0 0 / 34%) 0px 3px 8px;
    }
</style>
<section class="">
    <div class="footer-4">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="top-category">
                        <div class="row footer-theme">
                            <div class="col-md-6 col-lg-4">
                                <div class="footer-box">
                                    <div class="footer-title">
                                        <h5>top category</h5>
                                        <div class="line"></div>
                                    </div>
                                    <div class="footer-contant">
                                        <ul>
                                            <li><a href="allproducts.php">Special Products</a></li>
                                            <!-- <li><a href="index.php">Arrival Products</a></li> -->
                                            <!-- <li><a href="index.php">Bestselling Products</a></li> -->
                                            <li><a href="allproducts.php">Top selling Products</a></li>

                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="footer-box">
                                    <div class="footer-title">
                                        <h5>quick link</h5>
                                        <div class="line"></div>
                                    </div>
                                    <div class="footer-contant">
                                        <ul>
                                            <li><a href="about.php">about us</a></li>
                                            <li><a href="contact.php">contact us</a></li>
                                            <li><a href="term.php">terms & conditions</a></li>
                                            <li><a href="ShippingReturn.php">Shipping Return</a></li>
                                            <li><a href="privacy.php">privacy Policy</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="col-md-6 col-lg-3">
                                <div class="footer-box">
                                    <div class="footer-title">
                                        <h5>quick link</h5>
                                        <div class="line"></div>
                                    </div>
                                    <div class="footer-contant">
                                        <ul>
                                            <li><a href="#"> my account</a></li>
                                            <li><a href="vendor-login.php"> Vendor Login</a></li>
                                            <li><a href="privacy.php">privacy</a></li>
                                            <li><a href="return.php">Return</a></li>
                                            <li><a href="#">Our Story</a></li>
                                            <li><a href="shipping.php">Shipping Policy </a></li>


                                        </ul>
                                    </div>
                                </div>
                            </div> -->
                            <div class="col-md-6 col-lg-4">
                                <div class="footer-box footer-contact-box">
                                    <div class="footer-title">
                                        <h5>contact us</h5>
                                        <div class="line"></div>
                                    </div>
                                    <div class="footer-contant">
                                        <ul class="contact-list">
                                            <li><i class="fa fa-map-marker"></i> 192 D Block, Vikas Marg, Block F, Preet Vihar, New Delhi, Delhi - 110092<br></li>
                                            <li><i class="fa fa-phone"></i>call us: <span>7018931370</span></li>
                                            <li><i class="fa fa-envelope-o"></i>email us: <span style="text-transform:lowercase"> <EMAIL></span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="sub-footer">
            <div class="container">
                <div class="row">
                    <div class="col-xl-12 col-md-12 col-sm-12">
                        <div class="footer-end text-center" style="">
                            <p>© Copyrights <span>2024</span> Deva King 777 All rights reserved. by Hindustan Enterprises</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    </div>
    </div>
</section>
<style>
    .radio_f_sort {
        text-transform: uppercase;
        cursor: pointer;
        color: #777;
        font-weight: 600;
        letter-spacing: 0.5px;
    }
</style>
<script>
    function add_to_cart() {
        var product_id = $("#product_id").val();
        var price = $("#price").val();
        var color = $("#color_option").val();
        var size = $("#size_option").val();
        var sub_cat_id = $("#sub_cat_id").val();
        if (color == "") {
            $(".color_msg").html('<span style="font-size: 14px;color: #c75252;">Please select any color.</span>');
        } else if (size == "") {
            $(".size_msg").html('<span style="font-size: 14px;color: #c75252;">Please select any size.</span>');
        } else {
            $.ajax({
                url: 'ajax/add_to_cart.php',
                type: 'post',
                data: {
                    'product_id': product_id,
                    'price': price,
                    'color': color,
                    'size': size,
                    'sub_cat_id': sub_cat_id
                },

                beforeSend: function() {
                    // setting a timeout
                    $("#add-to-cart").html('loading...');
                    $("#add-to-cart").attr('disabled', true);
                    $("#text_add_to_cart").css('display', 'block');

                },
                success: function(data) {
                    $("#text_add_to_cart").css('display', 'none');
                    $("#add-to-cart").attr('disabled', true);
                    $("#add-to-cart").html('view cart');
                    $("#add-to-cart").attr("onclick", "");
                    $("#add-to-cart").attr('href', 'cart.php');
                    $(".cart-product").html(data);
                    $('html, body').animate({
                        scrollTop: ($('.top-menu-block').offset().top)
                    }, 1000);
                },
            });


        }

    }
</script>

<script>
    function buy_now() {
        var product_id = $("#product_id").val();
        var price = $("#price").val();
        var color = $("#color_option").val();
        var size = $("#size_option").val();
        var sub_cat_id = $("#sub_cat_id").val();
        if (color == "") {
            $(".color_msg").html('<span style="font-size: 14px;color: #c75252;">Please select any color.</span>');
        } else if (size == "") {
            $(".size_msg").html('<span style="font-size: 14px;color: #c75252;">Please select any size.</span>');
        } else {
            $.ajax({
                url: 'ajax/add_to_cart.php',
                type: 'post',
                data: {
                    'product_id': product_id,
                    'price': price,
                    'color': color,
                    'size': size,
                    'sub_cat_id': sub_cat_id
                },

                beforeSend: function() {
                    // setting a timeout
                    $("#buy-now").html('loading...');
                    $("#buy-now").attr('disabled', true);
                },
                success: function(data) {
                    $("#buy-now").attr('disabled', true);
                    $("#buy-now").html('buy now');
                    window.location.href = 'checkout.php';
                },
            });
        }
    }
</script>
<script>
    function search_submit() {
        if ($("#search").val() == "") {

        } else {
            $("#search-form").submit();
        }
    }
</script>

<div id="text_add_to_cart"><i class="fa fa-check-circle"></i> Product Added to Cart!</div>
<style>
    #text_add_to_cart {
        position: fixed;
        top: 56%;
        left: 50%;
        color: white;
        display: none;
        user-select: none;
        background: rgba(0, 0, 0, .7);
        padding: 1.6em 8em;
        transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        font-size: 17px;
        z-index: 1000;
    }
</style>