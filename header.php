<?php
$session_id = session_id();
?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
  integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
  crossorigin="anonymous" referrerpolicy="no-referrer" />
<style>
  .layout-header2 {
    background-color: #f8f7f3 !important;
    /* background-color: #D7AF8B !important ; */
    padding: 10px 0 !important;
  }
</style>
<header>
  <style>
    .top-header {
      background-color: #F2E9E9;
      padding: 10px 0;
      z-index: 99;
      color: #832729 !important;
    }

    .top-header .top-header-right .top-menu-block ul li a {
      color: #b14545;
      text-transform: capitalize;
      font-family: Raleway, sans-serif;
      font-size: 13px;
      font-weight: 800;
      letter-spacing: 1px;
    }

    .category-header-2 {
      background-color: #ffffff !important;
    }

    @media (max-width: 577px) {
      .mobile-fix-option {
        position: fixed;
        bottom: 0;
        left: 0;
        height: 45px;
        background-color: #ffffff;
      }
    }

    .category-header-2 .navbar-menu .category-left .icon-block ul li i {
      color: #832729;
      font-size: 31px;
    }

    @media only screen and (min-width: 320px) and (max-width: 440px) {
      .icon-block ul li a {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #832729;
        font-family: 'PT Sans';
      }
    }

    .category-header-2 .navbar-menu .category-left .menu-block .pixelstrap .dark-menu-item {
      padding-top: 15px;
      padding-bottom: 15px;
      color: #832729 !important;
    }

    .layout-header2 .main-menu-block .cart-block .cart i {
      font-size: 29px;
      color: #832729;
    }

    .title1 {
      background-color: #F2E9E9 !important;
    }

    @media only screen and (max-width: 600px) {
      .layout-header2 {
        background-color: #f8f7f3 !important;
        /* background-color: #D7AF8B !important; */
        padding: 10px 0 !important;
        position: fixed;
        width: 100%;
        z-index: 9;
        top: 0;
      }
    }
  </style>
  <div class="mobile-fix-option"></div>
  <div class="top-header ">
    <div class="custom-container">
      <div class="row">
        <div class="col-xl-5 col-md-7 col-sm-6">
          <div class="top-header-left">
            <div class="shpping-order">
              <!--<h6><a href="vendor-login.php" style="color: #fff;">Become a seller</a></h6>-->

              <!-- <a href="https://web.whatsapp.com/" style="color: #b14545; font-size:20px; margin-right:10px;"><i class="fa-brands  fa-whatsapp" aria-hidden="true"></i></a>
               <a href="https://www.facebook.com/" style="color: #b14545; font-size:20px; margin-right:10px;"><i class="fa-brands  fa-facebook-square" aria-hidden="true"></i></a>
                <a href="https://twitter.com/i/flow/login" style="color: #b14545; font-size:20px; margin-right:10px;"><i class="fa-brands  fa-twitter-square" aria-hidden="true"></i></a>
                 <a href="https://www.youtube.com/" style="color: #b14545; font-size:20px; margin-right:10px;">    <i class="fa-brands  fa-youtube-play" aria-hidden="true"></i></a> -->
              <p>
                Use Code APP100 - Download the APP & get Flat Rs 100 Off!
                Download the App</p>

            </div>

          </div>
        </div>
        <div class="col-xl-7 col-md-5 col-sm-6 ">
          <div class="top-header-right">
            <div class="top-menu-block">
              <ul>
                <!-- <li><a href="#"><i class="fa fa-bell"></i> Notifications</a></li> -->
                <li><a href="account.php"><i class="fa fa-user-circle-o"></i> Account</a></li>
                <?php if (empty($_SESSION['customer_name'])) { ?>
                  <li><a href="login.php"><i class="fa fa-sign-in" aria-hidden="true"></i> Login</a></li>
                <?php } else { ?>
                  <li><a href="account.php"><i class="fa fa-sign-in" aria-hidden="true"></i>
                      <?php echo ucfirst($_SESSION['customer_name']); ?></a></li>
                <?php } ?>

              </ul>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>




</header>
<!--header end-->

<style>
  .stciky-nav {
    position: sticky !important;
    top: 0;
    z-index: 11;
  }
</style>

<section class="stciky-nav">
  <div class="layout-header2 ">
    <div>
      <div class="col-md-12">
        <div class="main-menu-block  justify-content-between">
          <!-- D7AF8B -->
          <div class="logo-block">
            <a href="index.php" style="color: #fb778f">
              <!-- <img src="./assets/img/logo/why.png" alt=""> -->
              <h3 style="color: #832729">Deva King 777</h3>
            </a>
          </div>


          <div class="cart-block cart-hover-div align-items-center flex-column"
            onclick="window.loction.href='cart.php'">
            <?php $sel_count = dbQuery("SELECT sum(qty) as TtlCount FROM tabl_cart WHERE session_id='" . $session_id . "'");
            $res_count = dbFetchAssoc($sel_count);
            if ($res_count['TtlCount'] == "") {
              $cart = 0;
            } else {
              $cart = $res_count['TtlCount'];
            }
            ?>
            <div class="cart ">
              <span class="cart-product"><?php echo $cart ?></span>
              <ul class="d-flex" style="gap: 12px;">
                <li class="mobile-cart  ">
                  <a href="cart.php">
                    <i class="fa fa-shopping-cart"></i>
                  </a>
                </li>
                <li class="mobile-cart  ">
                  <a href="wishlist.php">
                    <i class="fa-regular fa-heart"></i>
                  </a>
                </li>
              </ul>
            </div>
            <!-- <div class="cart-item ml-0">
    
              <h5 class="ml-0">cart</h5>
            </div> -->
          </div>
          <div class="menu-nav d-block d-lg-none ">
            <span class="toggle-nav">
              <i class="fa fa-bars "></i>
            </span>
          </div>
        </div>
      </div>
      <!-- lllllllllll -->
    </div>
  </div>




  <div class="d-md-none d-block" style="padding-top:70px;"></div>



  <div class="category-header-2">
    <div class="custom-container">
      <div class="row">
        <div class="col">
          <div class="navbar-menu justify-content-center">
            <div class="category-left">

              <div class="menu-block">
                <nav id="main-nav">
                  <div class="toggle-nav"><i class="fa fa-bars sidebar-bar"></i></div>
                  <ul id="main-menu" class="sm pixelstrap sm-horizontal">
                    <li class="back_btn">
                      <div class="mobile-back text-right">Back<i class="fa fa-angle-right pl-2" aria-hidden="true"></i>
                      </div>
                    </li>
                    <li>
                      <a href="index.php" class="dark-menu-item">Home</a>
                    </li>

                    <?php $sel_cat = dbQuery("SELECT * FROM tabl_category ORDER BY name ASC");
                    while ($res_cat = dbFetchAssoc($sel_cat)) { ?>
                      <li class="mega">
                        <a href="#" class="dark-menu-item"><?php echo strtoupper($res_cat['name']) ?></a>
                        <ul class="mega-menu full-mega-menu ratio_landscape">
                          <li>
                            <div class="container">
                              <div class="row">
                                <?php $sub_cat = dbQuery("SELECT * FROM tabl_sub_category WHERE parent_id='" . $res_cat['id'] . "' ORDER BY name ASC");
                                while ($res_sub_cat = dbFetchAssoc($sub_cat)) { ?>
                                  <div class="col-12 col-xl-3 mega-box">
                                    <div class="link-section">
                                      <div class="menu-title">
                                        <h5>
                                          <a href="product.php?cateoryID=<?php echo $res_sub_cat['id']; ?>">
                                            <?php echo ucfirst($res_sub_cat['name']); ?>
                                          </a>
                                        </h5>
                                      </div>

                                    </div>
                                  </div>
                                <?php } ?>
                              </div>
                            </div>
                          </li>
                        </ul>
                      </li>
                    <?php } ?>
                  </ul>
                </nav>
              </div>

            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</section>


<!--header start-->
<header class="d-none mobile_show">
  <div class="mobile-fix-option">
    <div class="category-header-2">
      <div class="custom-container">
        <div class="row">
          <div class="col">
            <div class="navbar-menu">
              <div class="category-left">

                <div class="icon-block">
                  <ul>
                    <li class="mobile-search"><a href="index.php"><i class="fa fa-home"></i> Home</a>
                    <li class="mobile-setting mobile-setting-hover"><a href="category.php" class="cate"><i
                          class="fa fa-th-large"></i> Categories</a></li>
                    <li class="mobile-wishlist"><a href="whatsapp.php"><i class="fa-brands fa-whatsapp"></i>Whatsapp</a>
                    </li>
                    <li class="mobile-user onhover-dropdown"><a href="account.php"><i class="fa fa-user"></i>
                        Account</a></li>
                    <li class="mobile-user onhover-dropdown"><a href="order-history.php"><i
                          class="fa-solid fa-cart-shopping"></i> Order</a></li>

                    <!-- <div class ="search-overlay">
                      <div>
                        <span class="close-mobile-search">×</span>
                        <div class="overlay-content">
                          <div class="container">
                            <div class="row">
                              <div class="col-xl-12">
                                <form>
                                  <div class="form-group"><input type="text" class="form-control" id="exampleInputPassword1" placeholder="Search a Product"></div>
                                  <button type="submit" class="btn btn-primary"><i class="fa fa-home"></i></button>
                                </form>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div> -->
                    </li>
                    </li>
                  </ul>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="layout-header2 d-md-block d-none">
    <div class="container">
      <div class="col-md-12">
        <div class="main-menu-block">

          <div class="cart-block cart-hover-div ">
            <div class="cart ">
              <span class="cart-product">0</span>
              <ul>
                <li class="mobile-cart  ">
                  <a href="#">
                    <i class="fa fa-shopping-cart"></i>
                  </a>
                </li>
              </ul>
            </div>
            <div class="cart-item">
              <h5>shopping</h5>
              <h5>cart</h5>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>