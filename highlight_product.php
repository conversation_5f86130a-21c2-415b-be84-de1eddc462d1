<?php session_start();
include ('admin/lib/db_connection.php');
include ('admin/lib/get_functions.php'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
  <title>Deva King 777</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <!-- <link rel="stylesheet" href="xx.jpg"> -->
  <link rel="icon" href="./assets/img/logo/icon.png">
  <!--Google font-->
  <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
  <!--icon css-->
  <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
  <link rel="stylesheet" type="text/css" href="assets/css/themify.css">

  <!--Slick slider css-->
  <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
  <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">
  <!--Animate css-->
  <link rel="stylesheet" type="text/css" href="assets/css/animate.css">
  <!-- Bootstrap css -->
  <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">
  <!-- Theme css -->
  <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
  <!-- custom responsive css -->
  <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">

  <style>
    .likeicon {
      position: absolute;
      z-index: 8;
      top: 0;
      right: 0;
      text-align: right;
      padding: 11px;
      color: brown;
      font-size: 20px;
    }
  </style>
</head>

<body class="bg-light ">
  <!-- loader start -->
  <div class="loader-wrapper">
    <div>
      <!-- <img src="assets/images/loader.gif" alt="loader"> -->
      <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
    </div>
  </div>
  <!-- loader end --><!--header start-->
  <?php include ('header.php') ?>
  <!--header end--><!-- section start -->
  <section class="section-big-pt-space ratio_asos bg-light mb-30 ">
    <div class="collection-wrapper">
      <div class="custom-container">
        <div class="row">
          
          <div class="collection-content col">
            <div class="page-main-content">
              <div class="row">
                <div class="col-sm-12">
                  <div class="collection-product-wrapper">
                    <div class="product-wrapper-grid">
                      <div class="row">
                        <?php if (@$_REQUEST['f_sort'] == 1) {
                          $fsort_qry = 'ORDER BY tabl_product_filter.price ASC';
                        } elseif (@$_REQUEST['f_sort'] == 2) {
                          $fsort_qry = 'ORDER BY tabl_product_filter.price DESC';
                        } elseif (@$_REQUEST['f_sort'] == 3) {
                          $fsort_qry = 'ORDER BY tabl_product_filter.total_review DESC';
                        } elseif (@$_REQUEST['f_sort'] == 4) {
                          $fsort_qry = 'ORDER BY tabl_product_filter.total_sales DESC';
                        } else {
                          $fsort_qry = '';
                        }
                        if (@$_REQUEST['color'] != "") {
                          $color_qry = '';
                          $i = 1;
                          foreach ($_REQUEST['color'] as $color) {
                            if ($i == 1) {
                              $color_qry .= 'AND FIND_IN_SET(' . $color . ', tabl_product_filter.color )';
                            } else {
                              $color_qry .= 'OR FIND_IN_SET(' . $color . ', tabl_product_filter.color )';
                            }
                            $i++;
                          }
                        } else {
                          $color_qry = '';
                        }
                        if (@$_REQUEST['size'] != "") {
                          $size_qry = '';
                          $i = 1;
                          foreach ($_REQUEST['size'] as $size) {
                            if ($i == 1) {
                              $size_qry .= 'AND FIND_IN_SET(' . $size . ', tabl_product_filter.size )';
                            } else {
                              $size_qry .= 'OR FIND_IN_SET(' . $size . ', tabl_product_filter.size )';
                            }
                            $i++;
                          }
                        } else {
                          $size_qry = '';
                        }
                        if (@$_REQUEST['availablity'] != "") {
                          $av_qry = '';
                          $i = 1;
                          foreach ($_REQUEST['availablity'] as $ava) {
                            $av_qry .= '*';
                            $i++;
                          }
                        } else {
                          $av_qry = '(in_stock-out_stock>0)';
                        }
                        if (@$_REQUEST['price'] != "") {
                          $price_qry = '';
                          $i = 1;
                          foreach ($_REQUEST['price'] as $price) {
                            if ($i == 1) {
                              $condition = 'AND';
                            } else {
                              $condition = 'OR';
                            }
                            if ($price == 1) {
                              $price_qry .= ' ' . $condition . ' tabl_product_filter.price<=499';
                            } elseif ($price == 2) {
                              $price_qry .= ' ' . $condition . ' tabl_product_filter.price>=500 AND tabl_product_filter.price<=999';
                            } elseif ($price == 3) {
                              $price_qry .= ' ' . $condition . ' tabl_product_filter.price>=1000 AND tabl_product_filter.price<=1999';
                            } elseif ($price == 4) {
                              $price_qry .= ' ' . $condition . ' tabl_product_filter.price>=2000';
                            }
                            $i++;
                          }
                        } else {
                          $price_qry = '';
                        }
                        // $sel = dbQuery("SELECT tabl_products.*,tabl_product_filter.* FROM tabl_products INNER JOIN tabl_product_filter ON tabl_products.id=tabl_product_filter.p_id WHERE tabl_product_filter.sub_cat_id='" . $_REQUEST['cateoryID'] . "' $color_qry  $size_qry $price_qry $fsort_qry");

                        $sel = dbQuery("
                            SELECT tp.*, tp.id as p_id
                            FROM tabl_hl_cat_pro AS thp
                            JOIN tabl_products AS tp ON tp.id = thp.product_id
                            WHERE thp.category_id = '" . $_REQUEST['hl_cat_id'] . "'
                        ");

                        while ($res = dbFetchAssoc($sel)) {
                          $sel_stock = dbQuery("SELECT $av_qry FROM tabl_stock WHERE p_id='" . $res['p_id'] . "'");
                          $num = dbNumRows($sel_stock);

                          if (isset($_SESSION['user_id'])) {
                            $sel_like = dbQuery("SELECT * FROM tabl_likes WHERE product_id='" . $res['p_id'] . "' AND user_id = '" . $_SESSION['user_id'] . "'");
                            $num_like = dbNumRows($sel_like);
                            $is_liked = $num_like > 0 ? "fa-solid" : "fa-regular";
                          } else {
                            $is_liked = 'fa-regular';
                          }

                          if ($num > 0) {
                            $off = (($res['old_price'] - $res['price']) / $res['old_price']) * 100;
                            $off = round($off, 0); ?>
                            <div class="col-xl-3 col-md-4 col-6  col-grid-box">
                              <div class="product">
                                <div class="product-box">
                                  <div class="product-imgbox">
                                    <a href="product-detail.php?productID=<?php echo $res['p_id']; ?>">
                                      <div class="product-front"> <img
                                          src="assets/images/products/<?php echo $res['image']; ?>" class="img-fluid"
                                          alt="product"> </div>
                                    </a>

                                    <div class="likeicon" onclick="likeProduct(event, <?php echo $res['p_id']; ?>, 1)">
                                      <i class="<?= $is_liked; ?> fa-heart" id="like_icon_1_<?= $res['p_id']; ?>"></i>
                                    </div>
                                    <div class="share-btn" onclick="copyLinkToClipboard('<?= $res['p_id']; ?>')">
                                      <i class="fa-solid fa-share"></i>
                                    </div>

                                    <div class="new-label">
                                      <div><?php echo $off ?>%</div>
                                    </div>
                                  </div>
                                  <div class="product-detail detail-center ">
                                    <div class="detail-title">
                                      <div class="detail-left"> <a href="#">
                                          <h6 class="price-title"> <?php echo $res['name']; ?> </h6>
                                        </a> </div>
                                      <div class="detail-right">
                                        <div class="check-price"> Rs. <?php echo $res['old_price']; ?> </div>
                                        <div class="price">
                                          <div class="price"> Rs. <?php echo $res['price']; ?> </div>
                                        </div>
                                      </div>
                                    </div>
                                    <a href="product-detail.php?productID=<?php echo $res['p_id']; ?>"
                                      class="btn btn-rounded mtb-20 btn-blue">Add To Cart </a>
                                    <a href="product-detail.php?productID=<?php echo $res['p_id']; ?>"
                                      class="btn btn-rounded mtb-20">Buy Now </a>
                                  </div>
                                </div>
                              </div>
                            </div>
                          <?php }
                        } ?>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- section End --><!--footer-start-->
  <?php include ('footer.php') ?>
  <!--footer-end--><!-- tap to top -->
  <div class="tap-top">
    <div> <i class="fa fa-angle-double-up"></i> </div>
  </div>
  <!-- tap to top End --><!-- latest jquery-->
  <script src="assets/js/jquery-3.3.1.min.js"></script><!-- menu js-->
  <script src="assets/js/menu.js"></script><!-- popper js-->
  <script src="assets/js/popper.min.js"></script><!-- slick js-->
  <script src="assets/js/slick.js"></script><!-- Bootstrap js-->
  <script src="assets/js/bootstrap.js"></script><!-- Theme js-->
  <script src="assets/js/script.js"></script>
</body>
<style>
  .product .product-box:hover .product-detail.detail-center .detail-title {
    opacity: 1;
  }

  .section-big-pt-space {
    padding-top: 0px;
  }

  .mt-30 {
    margin-top: 30px;
  }

  .mb-30 {
    margin-bottom: 30px;
  }

  .mt-50 {
    margin-top: 50px;
  }

  .mtb-20 {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .btn-rounded {
    font-size: 13px;
    padding: 10px 11px;
    font-weight: 700;
    color: #fff !important;
    /* background-color: #cabdb4;*/
    background-color: #ff708a;
    border-radius: 25px;
    position: relative;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    line-height: 1;
    display: inline-block;
    letter-spacing: 0.05em;
  }

  .btn-rounded:hover {
    background-color: #ff708a;
  }

  .instagram-box img {
    max-width: 145px;
    height: 145px;
  }

  .product .product-box {
    margin-right: 1px;
    margin-top: 15px;
    padding-right: 0px;
  }

  .product .product-box .product-imgbox img {
    margin: 0 auto;
    max-width: 100%;
    height: auto;
    padding: 0 35px;
  }

  @media (max-width: 577px) {
    .mobile-fix-option {
      position: fixed;
      bottom: 0;
      left: 0;
      height: 45px;
      background-color: #ff708a;
    }
  }
</style>
<script>$(document).ready(function () {
    $(".f_sort").click(function () {
      var f_sort = ''; $("input[name='f_sort']:checked");
      var sor_par = $(this).val();
      var url = $(location).attr('href');
      var urlParams = new URLSearchParams(window.location.search);
      //get all parametersvar
      is_param = urlParams.get('f_sort');
      if (is_param) { var url = new URL(url); var search_params = url.searchParams; search_params.set('f_sort', $(this).val()); url.search = search_params.toString(); var url = url.toString(); var url = decodeURI(url); console.log(url); } else { var url = '' + url + '&f_sort=' + $(this).val() + ''; } window.history.pushState('page2', 'Title', url); var get_url = url.split('?'); $.ajax({ url: 'ajax/get_filter_page.php?' + get_url[1] + '', type: 'get', beforeSend: function () { $(".product-wrapper-grid").html('<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>'); }, success: function (data) { $(".product-wrapper-grid").html(data); $('html, body').animate({ scrollTop: $(".product-wrapper-grid").offset().top }, 500); }, });
    });
  });</script>
<script>$(document).ready(function () { $(".sort_color").click(function () { var color_code = []; $.each($("input[name='color']:checked"), function () { color_code.push($(this).val()); }); if ($(this).prop("checked") == true) { var url = $(location).attr('href'); var new_url = '' + url + '&color[]=' + $(this).val() + ''; window.history.pushState('page2', 'Title', new_url); var get_url = new_url.split('?'); } else { var url = $(location).attr('href'); var avoid = '&color[]=' + $(this).val() + ''; var url = url.replace(avoid, ''); window.history.pushState('page2', 'Title', '' + url + ''); var get_url = url.split('?'); } $.ajax({ url: 'ajax/get_filter_page.php?' + get_url[1] + '', type: 'get', beforeSend: function () { $(".product-wrapper-grid").html('<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>'); }, success: function (data) { $(".product-wrapper-grid").html(data); $('html, body').animate({ scrollTop: $(".product-wrapper-grid").offset().top }, 500); }, }); }); });</script>
<script>$(document).ready(function () { $(".sort_size").click(function () { var size_code = []; $.each($("input[name='size']:checked"), function () { size_code.push($(this).val()); }); if ($(this).prop("checked") == true) { var url = $(location).attr('href'); var new_url = '' + url + '&size[]=' + $(this).val() + ''; window.history.pushState('page2', 'Title', new_url); var get_url = new_url.split('?'); } else { var url = $(location).attr('href'); var avoid = '&size[]=' + $(this).val() + ''; var url = url.replace(avoid, ''); window.history.pushState('page2', 'Title', '' + url + ''); var get_url = url.split('?'); } $.ajax({ url: 'ajax/get_filter_page.php?' + get_url[1] + '', type: 'get', beforeSend: function () { $(".product-wrapper-grid").html('<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>'); }, success: function (data) { $(".product-wrapper-grid").html(data); $('html, body').animate({ scrollTop: $(".product-wrapper-grid").offset().top }, 500); }, }); }); });</script>
<script>$(document).ready(function () { $(".sort_av").click(function () { var av_code = []; $.each($("input[name='availablity']:checked"), function () { av_code.push($(this).val()); }); if ($(this).prop("checked") == true) { var url = $(location).attr('href'); var new_url = '' + url + '&availablity[]=' + $(this).val() + ''; window.history.pushState('page2', 'Title', new_url); var get_url = new_url.split('?'); } else { var url = $(location).attr('href'); var avoid = '&availablity[]=' + $(this).val() + ''; var url = url.replace(avoid, ''); window.history.pushState('page2', 'Title', '' + url + ''); var get_url = url.split('?'); } $.ajax({ url: 'ajax/get_filter_page.php?' + get_url[1] + '', type: 'get', beforeSend: function () { $(".product-wrapper-grid").html('<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>'); }, success: function (data) { $(".product-wrapper-grid").html(data); $('html, body').animate({ scrollTop: $(".product-wrapper-grid").offset().top }, 500); }, }); }); });</script>
<script>$(document).ready(function () { $(".sort_price").click(function () { var price_code = []; $.each($("input[name='price']:checked"), function () { price_code.push($(this).val()); }); if ($(this).prop("checked") == true) { var url = $(location).attr('href'); var new_url = '' + url + '&price[]=' + $(this).val() + ''; window.history.pushState('page2', 'Title', new_url); var get_url = new_url.split('?'); } else { var url = $(location).attr('href'); var avoid = '&price[]=' + $(this).val() + ''; var url = url.replace(avoid, ''); window.history.pushState('page2', 'Title', '' + url + ''); var get_url = url.split('?'); } $.ajax({ url: 'ajax/get_filter_page.php?' + get_url[1] + '', type: 'get', beforeSend: function () { $(".product-wrapper-grid").html('<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>'); }, success: function (data) { $(".product-wrapper-grid").html(data); $('html, body').animate({ scrollTop: $(".product-wrapper-grid").offset().top }, 500); }, }); }); });</script>




<script>
  function likeProduct(event, product_id, type) {
    event.stopPropagation();

    const icon = document.getElementById("like_icon_" + type + "_" + product_id);
    const like_text = document.getElementById("like_text");

    // alert("Like ID: like_" + propertyId);

    // Make an AJAX call to the like_dislike.php script
    const xhr = new XMLHttpRequest();
    xhr.open("POST", "./ajax/like_dislike.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4 && xhr.status === 200) {
        const response = JSON.parse(xhr.responseText);
        if (response.success) {
          // Toggle class and change title based on response
          // btn.classList.toggle("liked", response.isLiked);


          if (response.isLiked) {
            // if (icon.classList.contains('fa-regular')) {
            icon.classList.remove('fa-regular');
            icon.classList.add('fa-solid');
          } else {
            icon.classList.remove('fa-solid');
            icon.classList.add('fa-regular');
            // }
          }

          // like_text.innerHTML = response.isLiked ? "Liked" : "Like";
        } else {
          console.error("An error occurred: " + response.message);
          alert(response.message);
        }
      }
    };
    xhr.send("product_id=" + product_id);

  }


  function copyLinkToClipboard(pro_id) {
    // Create a temporary textarea element to hold the link
    const textarea = document.createElement('textarea');
    textarea.value = 'https://devaking777.com/product-detail.php?productID=' + pro_id;
    document.body.appendChild(textarea);

    // Select the text in the textarea
    textarea.select();
    textarea.setSelectionRange(0, 99999); // For mobile devices

    // Copy the selected text to the clipboard
    try {
      const successful = document.execCommand('copy');
      const message = successful ? 'Link copied to clipboard!' : 'Failed to copy link.';
      console.log(message);
      alert(message);
    } catch (err) {
      console.error('Oops, unable to copy:', err);
    }

    // Remove the temporary textarea element
    document.body.removeChild(textarea);
  }
</script>


</html>