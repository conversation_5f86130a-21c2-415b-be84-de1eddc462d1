<?php session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
//include('auth.php');
$page = 1;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>Deva King 777</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">

    <!-- <link rel="stylesheet" href="xx.jpg"> -->
    <link rel="icon" href="./assets/img/logo/icon.png">


    <!--Google font-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">


    <!--icon css-->
    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
    <link rel="stylesheet" type="text/css" href="assets/css/themify.css">

    <!--Slick slider css-->
    <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
    <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">

    <!--Animate css-->
    <link rel="stylesheet" type="text/css" href="assets/css/animate.css">
    <!-- Bootstrap css -->
    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">

    <!-- Theme css -->
    <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
    <!-- custom responsive css -->
    <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">
    <style>
        .header-bar .input-group input {
            box-shadow: none !important;
            border: none !important;


        }

        .input-group {
            border: 1px solid gray !important;

            /* z-index: 12; */
        }

        .input-group-text {
            background: none !important;
        }

        @media screen and(max-width : 767px) {
            .header-bar {
                margin-top: 10px;
            }
        }

        .reviews {
            background-color: #f2f0ce;
        }

        .reviews h4 {
            font-size: 25px;

        }

        .reviews i {
            color: #fbc02d !important;
            text-align: center;
        }

        .reviews .carousel-item img {
            width: 500px !important;
        }

        .silder-x {
            display: flex !important;
            justify-content: center !important;
        }

        .scroll-1::-webkit-scrollbar {
            display: none;
        }

        .slider_container {
            position: relative;
            width: 100%;
            min-width: 50rem;
            height: 30rem;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            overflow: hidden;
        }

        .slider {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            animation: 30s cubic-bezier(1, 0.95, 0.565, 1) sliding infinite;
        }

        .slide {
            position: relative;
            min-width: 100%;
            height: 100%;
        }

        .slide img {
            width: 100%;
            height: 100%;
        }

        .slide .caption {
            position: absolute;
            left: 0;
            bottom: 5%;
            font-size: 5rem;
            font-weight: 600;
            color: white;
            text-transform: capitalize;
            background: rgba(0, 0, 0, 0.348);
            backdrop-filter: blur(10px);
            padding: 1rem 5rem;
            border-radius: 0 2rem 2rem 0;
        }

        .slide.one {
            background: rgb(182, 19, 109);
            width: 100% !important;
        }

        .slide.two {
            background: rgb(255, 64, 64);
            width: 100% !important;
        }

        .slide.three {
            background: rgb(11, 173, 188);
            width: 100% !important;
        }

        .slide.four {
            background: rgb(11, 188, 14);
            width: 100% !important;
        }

        .slide.five {
            background: rgb(173, 11, 188);
            width: 100% !important;
        }

        @keyframes sliding {
            0% {
                transform: translateX(0%);
            }

            20% {
                transform: translateX(0%);
            }

            25% {
                transform: translateX(-100%);
            }

            45% {
                transform: translateX(-100%);
            }

            50% {
                transform: translateX(-200%);
            }

            70% {
                transform: translateX(-200%);
            }

            75% {
                transform: translateX(-300%);
            }

            95% {
                transform: translateX(-300%);
            }

            100% {
                transform: translateX(-400%);
            }
        }

        /* P */

        .category-header {
            display: flex;
            gap: 28px;
            /* padding: 30px; */
            padding: 10px 10px 10px 10px;
            justify-content: space-between !important;
            width: 100%;
            background-color: #ffffffff;
            /* overflow: hidden; */
        }


        .category-items img {
            border: 1px solid #832729;
            width: 113px;
            height: 113px;
            border-radius: 50%;
        }

        .category-items img:hover {
            border: 4px dotted #832729;
        }

        .scroll-1 {
            overflow: scroll;

        }

        .scroll-1::-webkit-scrollbar {
            /* display: none; */
        }

        /* @media screen and(max-width : 767px){

    } */
        .fist-slider .carousel-caption {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
        }

        @media only screen and (max-width: 600px) {
            .fist-slider .carousel-caption {
                height: 75%;
            }
        }

        @media only screen and (max-width: 600px) {
            .category-items img {
                border: 1px solid #832729;
                width: 65px;
                height: 65px;

            }

            .category-header {
                padding: 9px 0px 9px 14px;
                gap: 19px;
            }

            /* .category-items h6{
                font-size: 15px;
            } */
            .centered {
                left: 38% !important;
            }
        }

        .centered {
            color: #ffffff !important;
            color: #ffffff !important;
            font-size: 17px;
            font-weight: 00;
            font-style: italic;
        }

        .category-items h6 {
            font-size: 15px;
            color: gray;
            padding: 10px 0 0 0;
        }

        /* P */
        .silder-h {
            background-color: rgb(255 255 255 / 87%);
            color: #832729;
            padding: 5px;
            border-radius: 5px;
        }

        .shop-btn {
            border: 1px solid #ffffff;
            font-size: 17px;
            /* padding: 5px; */
            border-radius: 5px;
            margin-top: 10px;
        }

        /* .layout-header2 .main-menu-block .input-block .input-box .big-deal-form .input-group .form-control {
            border-radius: 0px 10px 10px 0px;
            border: none;
            border-left: 1px solid #ddd;
            border-right: 1px solid #ddd;
        }

        .layout-header2 .main-menu-block .input-block .input-box .big-deal-form {
            width: 100%;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 10px;
        }

        .layout-header2 .main-menu-block .cart-block {
            margin-left: 18px;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
        } */

        .img-wrapperr1 img {
            margin: 0 auto;
            border-radius: 50%;
            border: solid #ff708a;
        }

        .img-wrapperr1 img:hover {
            margin: 0 auto;
            border-radius: 50%;
            border: dotted #832729;
            /* transform: rotate(); */
        }

        @media screen and (max-width: 767px) {
            a.btn.btn-rounded {
                font-size: 9px !important;
            }
        }

        .img-wrapperr1 img {
            margin: 0 auto;
            border-radius: 50%;
            border: solid #832729;
        }

        .services {
            background-color: #F2E9E9;
            color: #832729 !important;
        }

        .btn-rounded {
            font-size: 13px;
            padding: 10px 18px;
            font-weight: 700;
            color: #fff !important;
            /* background-color: #cabdb4; */
            background-color: #832729 !important;
        }

        .cat-1 {
            /* border: 1px solid; */
            display: flex;
            justify-content: center;
            gap: 10px;
            min-width: 100%;
            /* overflow: scroll; */
        }

        .img-wrapperr1 img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
        }

        @media screen and (max-width: 767px) {
            .img-wrapperr1 img {
                width: 60px;
                height: 60px;
                border-radius: 50%;
            }
        }

        .custom-container-2 {
            position: relative;
        }

        .custom-container-2 a {
            text-decoration: none;
            color: black;
        }

        .view-all {
            position: absolute;
            bottom: -163px;
            width: 100%;
            color: black;
            padding: 10px;
            font-size: 20px;
            font-weight: 600;
            text-align: center;
            background-color: #ff708a;
            display: none;
            transition: transform .6s;
        }

        .custom-container-2 .col {
            overflow: unset;
        }

        .custom-container-2:hover .view-all {
            display: block;
            -ms-transform: scale(0.1);
            /* IE 9 */
            -webkit-transform: scale(0.1);
            /* Safari 3-8 */
            transform: scale(1.1);
        }

        @media only screen and (min-width: 320px) and (max-width: 440px) {
            .banner_promt {
                background: none !important;
                padding-top: 16px;
            }

            .title1 {
                text-align: center;
                padding: 5px;
            }
        }

        .custom-container .col {
            overflow: hidden;
        }

        .custom-container-2 img {
            transition: transform .6s;
        }

        .custom-container-2 img:hover {
            -ms-transform: scale(0.1);
            /* IE 9 */
            -webkit-transform: scale(0.1);
            /* Safari 3-8 */
            transform: scale(1.1);
        }

        .btnp {
            border-radius: 10px;
        }
    </style>
</head>

<body class="bg-light ">

    <!---------------------------------------- loader start ---------------------------------------->
    <!-- <div class="loader-wrapper">
        <div>
            <img src="./assets/images/loader-img.webp">
        </div>
    </div> -->
    <!---------------------------------------- loader end ---------------------------------------->




    <!-------------------------------------- header start -------------------------------------->
    <?php include('header.php') ?>
    <!-------------------------------------- header end -------------------------------------->


    <style>

    </style>

    <div class="p-3"></div>
    <div class="container header-bar">
        <div class="">
            <form action="search.php">
                <div class="input-group rounded">
                    <input type="search" name="search" class="form-control rounded" placeholder="Search Category"
                        aria-label="Search" aria-describedby="search-addon" />
                    <span class="input-group-text border-0" id="search-addon">
                        <i class="fas fa-search"></i>
                    </span>
                </div>
            </form>
        </div>
    </div>
    <div class="p-1"></div>
    <!-------------------------------------- slider start -------------------------------------->
    <!-- <section class="theme-slider b-g-white " id="theme-slider">
        <div class="custom-container">
            <div class="row">
                <div class="col-md-12 p-0">
                    <div class="slide-1 no-arrow">
                        <?php $slider = dbQuery("SELECT * FROM tabl_main_slider ORDER BY id asc");
                        while ($res_slider = dbFetchAssoc($slider)) { ?>
                            <div>
                                <div class="slider-banner p-center slide-banner-1">
                                    <div class="slider-img">
                                        <img src="assets/images/slider/thumb-1300/<?php echo $res_slider['image'] ?>" class="img-fluid" alt="slider">
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </section> -->
    <!-------------------------------------- slider end -------------------------------------->


    <style>
        .scroll-1 {
            /* border: 1px solid; */
            /* overflow-y: scroll; */
            width: 100%;
        }

        /* Hide scrollbar for Chrome, Safari and Opera */
        .example::-webkit-scrollbar {
            display: none;
        }

        /* Hide scrollbar for IE, Edge and Firefox */
        .example {
            -ms-overflow-style: none;
            /* IE and Edge */
            scrollbar-width: none;
            /* Firefox */
        }

        .myimg {
            width: 113px;
            height: 113px;
        }

        .myimg img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        .banimg {
            height: 440px;
            width: 100%;
        }

        .banimg img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        @media only screen and (max-width: 600px) {
            .myimg {
                width: 65px;
                height: 65px;
            }

            .banimg {
                height: 211px;
                width: 100%;
            }

            .layout-header2 {
                background-color: #f8f7f3 !important;
                /* background-color: #D7AF8B !important; */
                padding: 10px 0 !important;
                position: fixed;
                width: 100%;
                z-index: 9;
                top: 0;
            }
        }


        .custom-container .col {
            overflow: unset;
        }

        .likeicon {
            position: absolute;
            z-index: 8;
            top: 0;
            right: 0;
            text-align: right;
            padding: 11px;
            color: brown;
            font-size: 20px;
        }
    </style>




    <!-------------------------------------- category start -------------------------------------->
    <section class="container">
        <div class="scroll-1">
            <div class="category-header example">
                <?php
                // listing sub categories for home
                $subCatSql = dbQuery("SELECT * FROM tabl_highlight_category ORDER BY id DESC");
                if (mysqli_num_rows($subCatSql) > 0) {
                    while ($subhome_cat = dbFetchAssoc($subCatSql)) {
                ?>
                        <div class="category-items">
                            <a href="highlight_product.php?hl_cat_id=<?= $subhome_cat['id'] ?>">
                                <div>
                                    <div class="myimg">
                                        <img src="./assets/images/category/<?= $subhome_cat['image'] ?>" alt="">
                                    </div>

                                </div>
                                <h6 class="text-center"><?= $subhome_cat['name'] ?></h6>
                            </a>
                        </div>
                <?php
                    }
                }
                ?>
            </div>
        </div>
    </section>

    <!-------------------------------------- category end -------------------------------------->


    <!-- banner    note if you want caousel work you have add 3 js jquery---->
<div class="container-fluid fist-slider mt-3 p-0">
    <div id="carouselExampleSlidesOnly" class="carousel slide" data-ride="carousel" data-interval="3000">
        <div class="carousel-inner">
            <?php
            $sel_big_banner = dbQuery("SELECT * FROM tabl_single_banner");
            $isFirst = true;
            while($bigonebannr = dbFetchAssoc($sel_big_banner)) {
            ?>
                <div class="carousel-item <?= $isFirst ? 'active' : '' ?>">
                    <img src="./assets/images/banner/<?= $bigonebannr['image'] ?>" class="d-block w-100" alt="Banner">
                    <div class="carousel-caption">
                        <!-- Optional: Add headings or buttons -->
                        <!-- <h2 class="silder-h">Deva King 777</h2> -->
                        <!-- <p class="shop-btn"><a href="allproducts.php" class="btn text-light">Shop Now</a></p> -->
                    </div>
                </div>
            <?php
                $isFirst = false;
            }
            ?>
        </div>
    </div>
</div>

        <!-- banner -->

        <!-------------------------------------- title 1st Banner start -------------------------------------->


        <!------------------------------------------------- 1rd banner static banner------------------------------------------------->
        <!-- <section>
        <div class=" custom-container">
            <div class="row">
                <div class="col banner_promt collection-banner ">
                    <a href="allproducts.php" class="custom-container-2">
                        <img src="./assets/images/static-banner/ring.png" class="img-fluid" alt="">
                        <h6 class="view-all">View All</h6>
                    </a>
                </div>
                <div class="col d-lg-block  d-none">
                    <a href="allproducts.php" class="custom-container-2">
                        <img src="./assets/images/static-banner/earing.png" class="img-fluid" alt="">
                        <h6 class="view-all">View All</h6>
                    </a>
                </div>
            </div>
        </div>
    </section> -->

    </div>
    <!-- <div class="title1 section-my-space">
        <h4>BEST BUYS</h4>
    </div> -->
    <!--<section class="container-fluid">-->

    <!--    <div class="wedding-container ">-->
    <!--        <div class="row">-->
    <!--            <div class="col-12 col-lg-6 ">-->
    <!--                <div class="wedding-md-img">-->
    <!--                    <a href="allproducts.php"><img class="img-fluid" src="./assets/img/custom-products/_MG_4155.jpg" alt="">-->
    <!--   <span class="centered ">Shine Brighter, Sparkle Smarter: Discover Silver Fashion Jewelry's Timeless Charm. -->
    <!--<br> <br>   <span style=" border:1px solid #ffffff; color:yellow; padding:5px">Explore Now..</span>  </span>-->
    <!--                </a>-->
    <!--                </div>-->
    <!--            </div>-->
    <!--            <div class="col-12 col-lg-6 ">-->
    <!--                <div class="wedding-md-img">-->
    <!--                    <a href="allproducts.php"> <img class="img-fluid" src="./assets/img/custom-products/_MG_4154.jpg" alt=""></a>-->

    <!--                </div>-->
    <!--            </div>-->

    <!--        </div>-->
    <!--    </div>-->
    <!--</section>-->


    <!------------------------------------------------- 1st last banner static banner------------------------------------------------->
    <!-- <section class="banner_promt collection-banner section-py-space">
        <div class="custom-container">
            <div class="row collection2">
                <?php $big_banner2 = dbQuery("SELECT * FROM tabl_big_banner2 ORDER BY order_no ASC LIMIT 0,2");
                if ($big_num2 = dbNumRows($big_banner2) > 0) {
                    while ($res_big_banner2 = dbFetchAssoc($big_banner2)) { ?>
                        <div class="col-md-6 col-mb">
                            <div class="collection-banner-main banner-1">
                                <div class="collection-img img_ratio">
                                    <a href="javascript:void(0)"><img src="assets/images/banner/<?php echo $res_big_banner2['image'] ?>" class=""></a>
                                </div>
                            </div>
                        </div>

                <?php }
                }
                ?>
            </div>
        </div>
    </section>  -->
    <!-------------------------------------- title 1st Banner end -------------------------------------->




    <!---------------------------------------------- custom banner start ---------------------------------------------->
    <!-- <?php $banner1 = dbQuery("SELECT * FROM tabl_single_banner ORDER BY order_no ASC LIMIT 0,2");
            if ($num1 = dbNumRows($banner1) > 0) {
                $res_banner1 = dbFetchAssoc($banner1); ?>
        <section class="custom_banner d-sm-none">
            <div class="custom-container">
                <div class="wrap_banner1 wrap_banner d-flex">
                    <a href="#"><img src="assets/images/banner/thumb-1200/<?php echo $res_banner1['image'] ?>" alt="" class="img-fluid"></a>
                </div>
            </div>
        </section>
    <?php } ?> -->
    <!---------------------------------------------- custom banner end ---------------------------------------------->




    <!------------------------------------------ 3 <USER> <GROUP> banner start  ------------------------------------------>
    <!-- <section class="collection-banner section-py-space ">
        <div class="custom-container">
            <div class="row collection2">
                <?php $small_banner1 = dbQuery("SELECT * FROM tabl_small_banner3 ORDER BY order_no ASC LIMIT 0,3");
                if ($small_num1 = dbNumRows($small_banner1) > 0) {
                    while ($res_small_banner1 = dbFetchAssoc($small_banner1)) { ?>
                        <div class="col-md-4">
                            <div class="collection-banner-main banner-1">
                                <div class="collection-img">
                                    <a href="#"><img src="assets/images/banner/thumb-400/<?php echo $res_small_banner1['image'] ?>" class="" height="200px"></a>
                                </div>
                            </div>
                        </div>
                <?php }
                }
                ?>
            </div>
        </div>
    </section> -->
    <!------------------------------------------  3 <USER> <GROUP> banner end ------------------------------------------>
    <!-- <?php $banner2 = dbQuery("SELECT * FROM tabl_single_banner ORDER BY order_no ASC LIMIT 1,1");
            if ($num2 = dbNumRows($banner2) > 0) {
                $res_banner2 = dbFetchAssoc($banner2); ?>
        <section class="custom_banner d-sm-none">
            <div class="custom-container">
                <div class="wrap_banner2 wrap_banner">
                    <a href="#"><img src="assets/images/banner/thumb-1200/<?php echo $res_banner2['image'] ?>" alt="" class="img-fluid"></a>
                </div>
            </div>
        </section>
    <?php } ?> -->




    <style>
        @media only screen and (max-width: 600px) {
            .product .product-box .product-detail.detail-inverse .detail-title .detail-right .price {
                color: #ff708a;
                margin-left: 7px;
            }

            .product .product-box .product-detail .detail-title .detail-right .check-price {
                text-decoration: line-through;
                /* font-size: calc(12px +(14 - 12)*((100vw - 320px) /(1920 - 320))); */
                font-size: 10px;
            }
        }
    </style>



    <!--------------------------------------------- title start --------------------------------------------->
    <!-- <div class="title1 section-my-space">
        <h4>Special Products</h4>
    </div> -->
    <!--product start-->
    <!-- <section class="product section-pb-space">
        <div class="custom-container">
            <div class="row ">
                <div class="col pr-0">
                    <div class="product-slide-6 no-arrow">
                        <?php $special = dbQuery("SELECT * FROM tabl_products WHERE is_special='1' AND status='1'");
                        while ($res_special = dbFetchAssoc($special)) {
                            $special_off = (($res_special['old_price'] - $res_special['price']) / $res_special['old_price']) * 100;
                            $special_off = round($special_off, 0);
                        ?>
                            <div class="special-products-card" >
                                <div class="product-box border">
                                    <div class="product-imgbox">
                                        <a href="product-detail.php?productID=<?php echo $res_special['id']; ?>">
                                            <div class="product-front">
                                                <img src="assets/images/products/<?php echo $res_special['image']; ?>" class="img-fluid" alt="product">
                                            </div>
                                        </a>
                                        <div class="new-label">
                                            <div><?php echo $special_off; ?>%</div>
                                        </div>
                                    </div>
                                    <div class="product-detail detail-center detail-inverse">
                                        <div class="detail-title">
                                           
                                            <div class="detail-right">
                                                <div class="check-price">
                                                    Rs. <?php echo $res_special['old_price'] ?>
                                                </div>
                                                <div class="price">
                                                    <div class="price">
                                                        Rs. <?php echo $res_special['price'] ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="detail-left mt-1">
                                                <a href="#">
                                                    <h6 class="price-title">
                                                        <?php echo $res_special['name'] ?>
                                                    </h6>
                                                </a>
                                            </div>
                                            <a href="product-detail.php?productID=<?php echo $res_special['id']; ?>" class="btn btn-rounded mtb-20">Add To Cart </a>
                                            <a href="product-detail.php?productID=<?php echo $res_special['id']; ?>" class="btn btn-rounded mtb-20">Buy Now </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>

                    </div>
                </div>
            </div>
        </div>
    </section> -->
    <!--------------------------------------------- product end --------------------------------------------->








    <!---------------------------------------------- tabl_single_banner ---------------------------------------------->
    <!-- <?php $banner4 = dbQuery("SELECT * FROM tabl_single_banner ORDER BY order_no ASC LIMIT 3,1");
            if ($num4 = dbNumRows($banner4) > 0) {
                $res_banner4 = dbFetchAssoc($banner4); ?>
        <section class="custom_banner">
            <div class="custom-container">
                <div class="wrap_banner2 wrap_banner">
                    <a href="#"><img src="assets/images/banner/thumb-1200/<?php echo $res_banner4['image'] ?>" alt="" class="img-fluid"></a>
                </div>
            </div>
        </section>
    <?php } ?>  -->
    <!---------------------------------------------- tabl_single_banner ---------------------------------------------->










    <!--collection banner start-->
    <!-- <section class="collection-banner section-py-space">
        <div class="custom-container">
            <div class="row collection2">
                <?php $small_banner1 = dbQuery("SELECT * FROM tabl_small_banner3 ORDER BY order_no ASC LIMIT 3,3");
                if ($small_num1 = dbNumRows($small_banner1) > 0) {
                    while ($res_small_banner1 = dbFetchAssoc($small_banner1)) { ?>
                        <div class="col-md-4">
                            <div class="collection-banner-main banner-1">
                                <div class="collection-img">
                                    <a href="#"><img src="assets/images/banner/thumb-400/<?php echo $res_small_banner1['image'] ?>" class="" height="200px"></a>
                                </div>
                            </div>
                        </div>
                <?php }
                }
                ?>
            </div>
        </div>
    </section> -->
    <!--collection banner end-->

    <!-- <?php $banner3 = dbQuery("SELECT * FROM tabl_single_banner ORDER BY order_no ASC LIMIT 2,1");
            if ($num3 = dbNumRows($banner3) > 0) {
                $res_banner3 = dbFetchAssoc($banner3); ?>
        <section class="custom_banner">
            <div class="custom-container">
                <div class="wrap_banner2 wrap_banner">
                    <a href="#"><img src="assets/images/banner/thumb-1200/<?php echo $res_banner3['image'] ?>" alt="" class="img-fluid"></a>
                </div>
            </div>
        </section>
    <?php } ?>  -->




    <!------------------------------------------------- 2nd banner ------------------------------------------------->
    <!-- collection banner start-->
    <!-- <section class="banner_promt collection-banner section-py-space ">
        <div class="custom-container">
            <div class="row collection2">
                <?php $big_banner2 = dbQuery("SELECT * FROM tabl_big_banner2 ORDER BY order_no ASC LIMIT 0,2");
                if ($big_num2 = dbNumRows($big_banner2) > 0) {
                    while ($res_big_banner2 = dbFetchAssoc($big_banner2)) { ?>
                        <div class="col-md-6 col-mb">
                            <div class="collection-banner-main banner-1">
                                <div class="collection-img img_ratio">
                                    <a href="javascript:void(0)" ><img src="assets/images/banner/thumb-600/<?php echo $res_big_banner2['image'] ?>" class=""></a>
                                </div>
                            </div>
                        </div>

                <?php }
                }
                ?>
            </div>
        </div>
    </section> -->
    <!--collection banner end-->



    <!------------------------------------------------- 2nd banner static banner------------------------------------------------->

    <!------------------------------------------------- 2nd last banner static banner------------------------------------------------->





    <div class="title1 section-my-space">
        <h4>Top Selling</h4>
    </div>

    <section class="">

        <section class="product section-pb-space">
            <div class="custom-container">
                <div class="row ">
                    <div class="col pr-0">
                        <div class="product-slide-6 no-arrow">
                            <?php $trending = dbQuery("SELECT * FROM tabl_products WHERE is_gadget=1 AND status='1' ");
                            while ($res_trending = dbFetchAssoc($trending)) {
                                $trending_off = (($res_trending['old_price'] - $res_trending['price']) / $res_trending['old_price']) * 100;
                                $trending_off = round($trending_off, 0);


                                if (isset($_SESSION['user_id'])) {
                                    $sel_like = dbQuery("SELECT * FROM tabl_likes WHERE product_id='" . $res_trending['id'] . "' AND user_id = '" . $_SESSION['user_id'] . "'");
                                    $num_like = dbNumRows($sel_like);

                                    $is_liked = $num_like > 0 ? "fa-solid" : "fa-regular";
                                    // $is_liked_text = $num_like > 0 ? "Liked" : "Like";
                                } else {
                                    $is_liked = 'fa-regular';
                                    // $is_liked_text = "fa-regular";
                                }

                            ?>
                                <div>
                                    <div class="product-box">
                                        <div class="product-imgbox">
                                            <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>">
                                                <div class="product-front">
                                                    <img src="assets/images/products/<?php echo $res_trending['image']; ?>"
                                                        class="img-fluid" alt="product">
                                                </div>
                                            </a>
                                            <div class="likeicon"
                                                onclick="likeProduct(event, <?php echo $res_trending['id']; ?>, 1)">
                                                <i class="<?= $is_liked; ?> fa-heart"
                                                    id="like_icon_1_<?= $res_trending['id']; ?>"></i>
                                            </div>
                                            <div class="share-btn"
                                                onclick="copyLinkToClipboard('<?= $res_trending['id']; ?>')">
                                                <i class="fa-solid fa-share"></i>
                                            </div>

                                            <div class="new-label">
                                                <div>
                                                    <?php echo $trending_off; ?>%
                                                </div>
                                            </div>
                                        </div>
                                        <div class="product-detail detail-center detail-inverse">
                                            <div class="detail-title">
                                                <div class="detail-right">
                                                    <div class="check-price">
                                                        Rs.
                                                        <?php echo $res_trending['old_price'] ?>
                                                    </div>
                                                    <div class="price">
                                                        <div class="price">
                                                            Rs.
                                                            <?php echo $res_trending['price'] ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="detail-left mt-1">

                                                    <a href="#">
                                                        <h6 class="price-title">
                                                            <?php echo $res_trending['name'] ?>
                                                        </h6>
                                                    </a>
                                                </div>

                                                <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>"
                                                    class="btn btn-rounded mtb-20">Add To Cart </a>
                                                <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>"
                                                    class="btn btn-rounded mtb-20">Buy Now </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
        </section>
    </section>


    <!------------------------------------------------- Arrival Products ------------------------------------------------->
    <div class="title1 section-my-space">
        <h4>BESTSELLERS</h4>
    </div>
    <!--product start-->
    <section class="product section-pb-space">

        <div class="custom-container">
            <div class="row ">
                <div class="col pr-0">
                    <div class="product-slide-6 no-arrow">
                        <?php $new_arrival = dbQuery("SELECT * FROM tabl_products WHERE status='1' ORDER BY date_added DESC limit 0,30");
                        while ($res_arrival = dbFetchAssoc($new_arrival)) {
                            $arrival_off = (($res_arrival['old_price'] - $res_arrival['price']) / $res_arrival['old_price']) * 100;
                            $arrival_off = round($arrival_off, 0);


                            if (isset($_SESSION['user_id'])) {
                                $sel_like = dbQuery("SELECT * FROM tabl_likes WHERE product_id='" . $res_arrival['id'] . "' AND user_id = '" . $_SESSION['user_id'] . "'");
                                $num_like = dbNumRows($sel_like);

                                $is_liked = $num_like > 0 ? "fa-solid" : "fa-regular";
                                // $is_liked_text = $num_like > 0 ? "Liked" : "Like";
                            } else {
                                $is_liked = 'fa-regular';
                                // $is_liked_text = "fa-regular";
                            }

                        ?>
                            <div>
                                <div class="product-box">
                                    <div class="product-imgbox">
                                        <a href="product-detail.php?productID=<?php echo $res_arrival['id']; ?>">
                                            <div class="product-front">
                                                <img src="assets/images/products/<?php echo $res_arrival['image']; ?>"
                                                    class="img-fluid" alt="product">
                                            </div>
                                        </a>
                                        <div class="likeicon"
                                            onclick="likeProduct(event, <?php echo $res_arrival['id']; ?>, 2)">
                                            <i class="<?= $is_liked; ?> fa-heart"
                                                id="like_icon_2_<?= $res_arrival['id']; ?>"></i>
                                        </div>
                                        <div class="share-btn" onclick="copyLinkToClipboard('<?= $res_arrival['id']; ?>')">
                                            <i class="fa-solid fa-share"></i>
                                        </div>
                                        <div class="new-label">
                                            <div><?php echo $arrival_off; ?>%</div>
                                        </div>
                                    </div>
                                    <div class="product-detail detail-center detail-inverse">
                                        <div class="detail-title">
                                            <div class="detail-right">
                                                <div class="check-price">
                                                    Rs. <?php echo $res_arrival['old_price'] ?>
                                                </div>
                                                <div class="price">
                                                    <div class="price">
                                                        Rs. <?php echo $res_arrival['price'] ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="detail-left mt-1">
                                                <a href="#">
                                                    <h6 class="price-title">
                                                        <?php echo $res_arrival['name'] ?>
                                                    </h6>
                                                </a>
                                            </div>

                                            <a href="product-detail.php?productID=<?php echo $res_arrival['id']; ?>"
                                                class="btn btn-rounded mtb-20">Add To Cart </a>
                                            <a href="product-detail.php?productID=<?php echo $res_arrival['id']; ?>"
                                                class="btn btn-rounded mtb-20">Buy Now </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!------------------------------------------------- Arrival Products End ------------------------------------------------->





    <section class="container-fluid">
        <div class="">
            <div class="row">
                <?php
                $bigQur = dbQuery("SELECT * FROM tabl_small_banner2 LIMIT 2");
                while ($tabl_small_banner2 = dbFetchAssoc($bigQur)) {
                ?>
                    <div class="col-12 col-lg-6 ">
                        <div class="">
                            <a href="allproducts.php">
                                <div class="banimg">
                                    <img class="img-fluid" src="assets/images/banner/<?= $tabl_small_banner2['image'] ?>"
                                        alt="">
                                </div>

                            </a>
                        </div>
                    </div>
                <?php
                }
                ?>

            </div>
        </div>
    </section>


    <div class="section-my-space text-center mb-5">
        <a href="allproducts.php">
            <button type="button" class="btn border btnp" style="background-color: white; font-size: 19px;"><span
                    class="m-4">View More</span></button>
        </a>
    </div>


    <!------------------------------------------------- collection banner start ------------------------------------------------->
    <!-- <section class="collection-banner section-py-space ">
        <div class="custom-container">
            <div class="row collection2">
                <?php $small_banner2 = dbQuery("SELECT * FROM tabl_small_banner2 ORDER BY order_no ASC LIMIT 0,2");
                if ($small_num2 = dbNumRows($small_banner2) > 0) {
                    while ($res_small_banner2 = dbFetchAssoc($small_banner2)) { ?>
                        <div class="col-md-6">
                            <div class="collection-banner-main banner-1">
                                <div class="collection-img">
                                    <a href="javascript:void(0)"><img src="assets/images/banner/thumb-600/<?php echo $res_small_banner2['image'] ?>" class=""></a>
                                </div>
                            </div>
                        </div>

                <?php }
                }
                ?>
            </div>
        </div>
    </section> -->
    <!------------------------------------------------- collection banner end ------------------------------------------------->





    <!--product start-->
    <!-- <section class="product section-pb-space ">
        <div class="custom-container">
            <div class="row ">
                <div class="col pr-0">
                    <div class="product-slide-6 no-arrow">
                        <?php $sel_best = dbQuery("SELECT tabl_products.*,tabl_order_product.product_id FROM tabl_products INNER JOIN tabl_order_product ON tabl_products.id=tabl_order_product.product_id WHERE tabl_products.status='1' GROUP BY tabl_order_product.product_id");
                        while ($res_best = dbFetchAssoc($sel_best)) {
                            $best_off = (($res_best['old_price'] - $res_best['price']) / $res_best['old_price']) * 100;
                            $best_off = round($best_off, 0);
                        ?>



                            <div>
                                <div class="product-box">
                                    <div class="product-imgbox">
                                        <a href="product-detail.php?productID=<?php echo $res_best['id']; ?>">
                                            <div class="product-front">
                                                <img src="assets/images/products/<?php echo $res_best['image']; ?>" class="img-fluid" alt="product">
                                            </div>
                                        </a>
                                        <div class="new-label">
                                            <div><?php echo $best_off; ?>%</div>
                                        </div>
                                    </div>
                                    <div class="product-detail detail-center detail-inverse">
                                        <div class="detail-title">
                                        <div class="detail-right">
                                                <div class="check-price">
                                                    Rs. <?php echo $res_best['old_price'] ?>
                                                </div>
                                                <div class="price">
                                                    <div class="price">
                                                        Rs. <?php echo $res_best['price'] ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="detail-left mt-1">

                                                <a href="#">
                                                    <h6 class="price-title">
                                                        <?php echo $res_best['name'] ?>
                                                    </h6>
                                                </a>
                                            </div>
                                          
                                            <a href="product-detail.php?productID=<?php echo $res_best['id']; ?>" class="btn btn-rounded mtb-20">Add To Cart </a>
                                            <a href="product-detail.php?productID=<?php echo $res_best['id']; ?>" class="btn btn-rounded mtb-20">Buy Now </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </section> -->
    <!--product end-->




    <!------------------------------------------------- 3rd banner static banner------------------------------------------------->
    <!-- <div class="section-my-space text-center mb-5">
        <a href="allproducts.php">
            <button type="button" class="btn border btnp" style="background-color: white; font-size: 19px;"><span class="m-4">View more</span></button>
        </a>
    </div> -->
    <!-- <section>
     
        <section class="container-fluid">
        <div class="wedding-container">
            <div class="row">
                <div class="col-12 col-lg-6 wedding-padding1">
                    <div class="wedding-md-img">
                        <a href="allproducts.php"><img class="img-fluid" src="./assets/images/static-banner/New folder/3.png" alt=""></a>
                    </div>
                </div>
                <div class="col-12 col-lg-6 wedding-padding2">
                    <div class="wedding-md-img">
                    <a href="allproducts.php"><img class="img-fluid" src="./assets/images/static-banner/New folder/6.png" alt=""></a>
                    </div>
                </div>
           
            </div>
        </div>
    </section>
        
  
    </section> -->
    <!------------------------------------------------- 3rd last banner static banner------------------------------------------------->









    <!---------------- <div class="title1 section-my-space">
        <h4>Top Trending Product</h4>
    </div>--------------------------------- Tranding Product ------------------------------------------------->

    <!--product start-->
    <!-- <section class="product section-pb-space ">
   
        <div class="custom-container">
            <div class="row ">
                <div class="col pr-0">
                    <div class="product-slide-6 no-arrow">
                        <?php $trending = dbQuery("SELECT * FROM tabl_products WHERE is_trending='1'  AND status='1'");
                        while ($res_trending = dbFetchAssoc($trending)) {
                            $trending_off = (($res_trending['old_price'] - $res_trending['price']) / $res_trending['old_price']) * 100;
                            $trending_off = round($trending_off, 0);
                        ?>
                            <div>
                                <div class="product-box">
                                    <div class="product-imgbox">
                                        <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>">
                                            <div class="product-front">
                                                <img src="assets/images/products/thumb-500/<?php echo $res_trending['image']; ?>" class="img-fluid" alt="product">
                                            </div>
                                        </a>
                                        <div class="new-label">
                                            <div><?php echo $trending_off; ?>%</div>
                                        </div>
                                    </div>
                                    <div class="product-detail detail-center detail-inverse">
                                        <div class="detail-title">
                                         
                                            <div class="detail-right">
                                                <div class="check-price">
                                                    Rs. <?php echo $res_trending['old_price'] ?>
                                                </div>
                                                <div class="price">
                                                    <div class="price">
                                                        Rs. <?php echo $res_trending['price'] ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="detail-left mt-1">

                                                <a href="#">
                                                    <h6 class="price-title">
                                                        <?php echo $res_trending['name'] ?>
                                                    </h6>
                                                </a>
                    </div>
                                                                <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>" class="btn btn-rounded mtb-20">Add To Cart </a>
                                            <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>" class="btn btn-rounded mtb-20">Buy Now </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
    </section> -->
    <!--product end-->
    <!------------------------------------------------- Tranding Product End ------------------------------------------------->




    <!------------------------------------------------- collection banner start ------------------------------------------------->
    <!-- <section class="collection-banner section-py-space">
        <div class="custom-container">
            <div class="row collection2">
                <?php $small_banner1 = dbQuery("SELECT * FROM tabl_small_banner3 ORDER BY order_no ASC LIMIT 6,3");
                if ($small_num1 = dbNumRows($small_banner1) > 0) {
                    while ($res_small_banner1 = dbFetchAssoc($small_banner1)) { ?>
                        <div class="col-md-4">
                            <div class="collection-banner-main banner-1">
                                <div class="collection-img">
                                    <a href="#"><img src="assets/images/banner/thumb-400/<?php echo $res_small_banner1['image'] ?>" class="" height="200px"></a>
                                </div>
                            </div>
                        </div>
                <?php }
                }
                ?>
            </div>
        </div>
    </section> -->


    <!------------------------------------------------- wedding Gift ------------------------------------------------->



    <!-- <div class="title1 section-my-space">
        <h4>Wedding Gift</h4>
    </div>
    <section class="container-fluid">
        <div class="wedding-container">
            <div class="row">
                <div class="col-12">
                    <div class="lg-img">
                        <a href="#"><img class="img-fluid" src="./assets/images/static-banner/1.png" alt=""></a>
                    </div>
                </div>
                <div class="col-6 wedding-padding1">
                    <div class="wedding-md-img">
                        <a href="#"><img src="./assets/images/new_img/d1.gif" alt=""></a>
                    </div>
                </div>
                <div class="col-6 wedding-padding2">
                    <div class="wedding-md-img">
                        <a href="#"> <img src="./assets/images/new_img/d2.gif" alt=""></a>
                    </div>
                </div>
                <div class="col-12">
                    <div class="lg-img">
                        <a href="#"><img class="img-fluid" src="./assets/images/new_img/d2.gif" alt=""></a>
                    </div>
                </div>
            </div>
        </div>
    </section> -->

    <!------------------------------------------------- Top Selling ------------------------------------------------->

    <!------------------------------------------------- services start new ------------------------------------------------->

    <!-- review -->


    <section class="container">
        <hr>
    </section>

    <!-- review -->


    <div id="demo" class="d-none"></div>
    <div id="demo1" class="d-none"></div>


    <div class="p-1"></div>
    <!------------------------------------------------- footer-start ------------------------------------------------->
    <?php include('footer.php') ?>
    <!------------------------------------------------- footer-end ------------------------------------------------->

    <!------------------------------------------------- tap to top ------------------------------------------------->
    <div class="tap-top">
        <div>
            <i class="fa fa-angle-double-up"></i>
        </div>
    </div>
    <!------------------------------------------------- tap to top End ------------------------------------------------->


    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"
        integrity="sha384-IQsoLXl5PILFhosVNubq5LC7Qb9DXgDA9i+tQ8Zj3iwWAwPtgFTxbJ8NT4GN1R8p"
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js"
        integrity="sha384-cVKIPhGWiC2Al4u+LWgxfKTRIcfu0JTxR+EQDz/bgldoEyl4H0zUF0QKbrJ0EcQF"
        crossorigin="anonymous"></script>
    <!-- latest jquery-->
    <script src="assets/js/jquery-3.3.1.min.js"></script>

    <!-- slick js-->
    <script src="assets/js/slick.js"></script>

    <!-- popper js-->
    <script src="assets/js/popper.min.js"></script>

    <!-- Timer js-->
    <script src="assets/js/menu.js"></script>

    <!-- Bootstrap js-->
    <script src="assets/js/bootstrap.js"></script>

    <!-- Bootstrap js-->
    <script src="assets/js/bootstrap-notify.min.js"></script>

    <!-- Theme js-->
    <script src="assets/js/script.js"></script>
    <script src="assets/js/slider-animat.js"></script>
    <script src="assets/js/timer.js"></script>
    <script src="assets/js/modal.js"></script>

    <script>
        function likeProduct2(element) {
            // Find the <i> tag inside the clicked element
            var icon = element.querySelector('i');

            // Toggle the classes to change the heart icon style

        }
    </script>




    <script>
        function likeProduct(event, product_id, type) {
            event.stopPropagation();

            const icon = document.getElementById("like_icon_" + type + "_" + product_id);
            const like_text = document.getElementById("like_text");

            // alert("Like ID: like_" + propertyId);

            // Make an AJAX call to the like_dislike.php script
            const xhr = new XMLHttpRequest();
            xhr.open("POST", "./ajax/like_dislike.php", true);
            xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        // Toggle class and change title based on response
                        // btn.classList.toggle("liked", response.isLiked);


                        if (response.isLiked) {
                            // if (icon.classList.contains('fa-regular')) {
                            icon.classList.remove('fa-regular');
                            icon.classList.add('fa-solid');
                        } else {
                            icon.classList.remove('fa-solid');
                            icon.classList.add('fa-regular');
                            // }
                        }

                        // like_text.innerHTML = response.isLiked ? "Liked" : "Like";
                    } else {
                        console.error("An error occurred: " + response.message);
                        alert(response.message);
                    }
                }
            };
            xhr.send("product_id=" + product_id);

        }


        function copyLinkToClipboard(pro_id) {
            // Create a temporary textarea element to hold the link
            const textarea = document.createElement('textarea');
            textarea.value = 'https://devaking777.com/product-detail.php?productID=' + pro_id;
            document.body.appendChild(textarea);

            // Select the text in the textarea
            textarea.select();
            textarea.setSelectionRange(0, 99999); // For mobile devices

            // Copy the selected text to the clipboard
            try {
                const successful = document.execCommand('copy');
                const message = successful ? 'Link copied to clipboard!' : 'Failed to copy link.';
                console.log(message);
                alert(message);
            } catch (err) {
                console.error('Oops, unable to copy:', err);
            }

            // Remove the temporary textarea element
            document.body.removeChild(textarea);
        }
    </script>


</body>
<style>
    @media screen and(max-width : 767px) {
        .footer-brand h5 {
            font-size: 18px;
        }
    }

    .title1 {
        text-align: center;
    }

    .wedding-padding1 {
        padding-right: 0.1rch;
        /* margin: 0 0 0 0 ; */
    }

    .wedding-padding2 {
        padding-left: 0.1rch;
    }

    .lg-img img {
        width: 100%;
        border-radius: 5px;
    }

    .lg-img {
        width: 100%;
        border-radius: 5px;
        margin-bottom: 5px;
        margin-top: 5px;
    }

    .wedding-md-img {
        width: 100%;
        position: relative;
        text-align: center;
    }

    /* @media screen and(max-width: 767px){
        .wedding-md-img img{
        width: 100%;
        border-radius: 5px;
        height: 200px;
    }
    } */
    .wedding-md-img img {
        width: 100%;
        border-radius: 5px;
        /* height: 284px; */
    }

    .centered {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .product .product-box:hover .product-detail.detail-center .detail-title {
        opacity: 1;
    }

    .section-big-pt-space {
        padding-top: 0px;
    }

    .mt-50 {
        margin-top: 50px;
    }

    .mtb-20 {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .btn-rounded {
        font-size: 13px;
        padding: 10px 18px;
        font-weight: 700;
        color: #fff !important;
        /* background-color: #cabdb4;*/
        background-color: #ff708a;
        border-radius: 25px;
        position: relative;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
        line-height: 1;
        display: inline-block;
        letter-spacing: 0.05em;
        font-style: italic;
    }

    .btn-rounded:hover {
        background-color: #ff708a;
    }


    .instagram-box img {
        max-width: 145px;
        height: 145px;
    }


    .product .product-box {
        margin-right: 15px;
        margin-top: 15px;
        padding-right: 0px;
    }

    .no-arrow .slick-next,
    .no-arrow .slick-prev {
        display: none !important;
    }
</style>

</html>