<?php
session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
//include('lib/auth.php');
include('admin/inc/resize-class.php');
$page = 0;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">

<head>

  <title>Deva King 777</title>

  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <meta name="viewport" content="width=device-width,initial-scale=1">


  <!-- <link rel="icon" href="xx.jpg"> -->



  <!--Google font-->
  <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">


  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">


  <!--icon css-->
  <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
  <link rel="stylesheet" type="text/css" href="assets/css/themify.css">

  <!--Slick slider css-->
  <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
  <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">

  <!--Animate css-->
  <link rel="stylesheet" type="text/css" href="assets/css/animate.css">
  <!-- Bootstrap css -->
  <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">

  <!-- Theme css -->
  <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
  <!-- custom responsive css -->
  <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">
</head>

<body class="bg-light ">

  <!-- loader start -->
  <div class="loader-wrapper">
    <div>
      <img src="assets/images/loader.gif" alt="loader">
    </div>
  </div>
  <!-- loader end -->

  <!--header start-->

  <?php include('header.php') ?>
  <!--header end-->


  <!--section start-->
  <section class="login-page section-big-py-space bg-light">
    <div class="custom-container">
      <div class="row">
        <div class="col-xl-6 col-lg-6 col-md-8 offset-xl-3 offset-lg-3 offset-md-2">
          <div class="theme-card">
            <h3 class="text-center">Login</h3>

            <img src="assets/images/Difference.gif" class="img-fluid  " alt="logo">
            <form class="theme-form" id="loginform" method="post">
              <div class="form-group">
                <label for="email">Email</label>
                <input type="text" name="username" autocomplete="user-name" class="form-control" id="email" placeholder="Enter your mobile number or email id" required="">
              </div>
              <div class="form-group">
                <label for="review">Password</label>
                <input type="password" name="password" autocomplete="new-password" class="form-control" id="review" placeholder="Enter your password" required="">
              </div>
              <button type="submit" id="submit" class="btn btn-normal">Login</button>
              <a class="float-right txt-default mt-2" href="forget-pwd.php" id="fgpwd">Forgot your password?</a>
            </form>
            <div class="loader_login"></div>
            <a href="register.php" class="txt-default pt-3 d-block">Create an Account</a>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!--Section ends-->


  <!--footer-start-->

  <?php include('footer.php') ?>
  <!--footer-end-->



  <!-- tap to top -->
  <div class="tap-top">
    <div>
      <i class="fa fa-angle-double-up"></i>
    </div>
  </div>
  <!-- tap to top End -->


  <!-- latest jquery-->
  <script src="assets/js/jquery-3.3.1.min.js"></script>

  <!-- menu js-->
  <script src="assets/js/menu.js"></script>

  <!-- popper js-->
  <script src="assets/js/popper.min.js"></script>

  <!-- slick js-->
  <script src="assets/js/slick.js"></script>

  <!-- Bootstrap js-->
  <script src="assets/js/bootstrap.js"></script>

  <!-- Theme js-->
  <script src="assets/js/script.js"></script>



  <script>
    $("#loginform").submit(function(e) {
      $("#submit").attr('disabled', true);
      $(".loader_login").html('<img src="loader.gif"></i> please wait...');
      $.ajax({
        url: 'ajax/customer_login.php',
        type: 'post',
        data: $("#loginform").serialize(),
        success: function(data) {
          if (data == 1) {
            window.location.href = 'account.php';
            //window.location.href='https://maddamall.com/account.php';
          } else if (data == 2) {
            $("#submit").attr('disabled', false);
            $(".loader_login").html('<div class="alert alert-danger" role="alert">Your account is inactive, please contact admin!</div>');
            setTimeout(function() {
              $(".loader_login").html('');
            }, 8000);
          } else if (data == 3) {
            $("#submit").attr('disabled', false);
            $(".loader_login").html('<div class="alert alert-danger" role="alert">Your account is deleted, please contact admin!</div>');
            setTimeout(function() {
              $(".loader_login").html('');
            }, 8000);
          } else {
            $("#submit").attr('disabled', false);
            $(".loader_login").html('<div class="alert alert-danger" role="alert">Either username or password are wrong!</div>');
            setTimeout(function() {
              $(".loader_login").html('');
            }, 8000);

          }
        },
      });
      e.preventDefault(); // avoid to execute the actual submit of the form.
    });
  </script>

</body>

</html>