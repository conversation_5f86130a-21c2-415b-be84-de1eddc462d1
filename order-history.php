<?php session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
include('lib/auth.php');
include('admin/inc/resize-class.php');
$page = 0;
$sub_page = 0;
$profile = 2;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>Deva King 777</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="icon" href="./assets/img/logo/icon.png">


    <!--Google font-->
    <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">


    <!--icon css-->
    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
    <link rel="stylesheet" type="text/css" href="assets/css/themify.css">

    <!--Slick slider css-->
    <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
    <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">

    <!--Animate css-->
    <link rel="stylesheet" type="text/css" href="assets/css/animate.css">
    <!-- Bootstrap css -->
    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">

    <!-- Theme css -->
    <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
    <!-- custom responsive css -->
    <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">
</head>

<body class="bg-light ">

    <!-- loader start -->
    <div class="loader-wrapper">
        <div>
            <!-- <img src="assets/images/loader.gif" alt="loader"> -->
            <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
        </div>
    </div>
    <!-- loader end -->

    <!--header start-->

    <?php include('header.php') ?>
    <!--header end-->


    <!-- breadcrumb start -->
    <div class="breadcrumb-main">
        <div class="container">
            <div class="row">
                <div class="col">
                    <div class="breadcrumb-contain">
                        <div>
                            <h2>order-history</h2>
                            <ul>
                                <li><a href="index.php">home</a></li>
                                <li><i class="fa fa-angle-double-right"></i></li>
                                <li><a href="order.php">Order</a></li>
                                <li><i class="fa fa-angle-double-right"></i></li>
                                <li><a href="order-history.php?order_id=<?php echo $_REQUEST['order_id'] ?>">order-history</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- breadcrumb End -->

    <!--section start-->
    <section class="cart-section order-history section-big-py-space">
        <div class="container">
            <div class="row">
                <div class="col-md-12 col-sm-12 col-xs-12 mb-20"><?php $sel = dbQuery("SELECT * FROM tabl_order WHERE customer_id='" . $_SESSION['customer_id'] . "' AND id='" . $_REQUEST['order_id'] . "'");
                                                                    $res = dbFetchAssoc($sel);
                                                                    $state = dbQuery("SELECT name FROM tabl_state WHERE id='" . $res['state'] . "'");
                                                                    $res_state = dbFetchAssoc($state); ?>
                    <div class="box-content ord-summary-div">
                        <p><b>Order# :</b> Deva King 777-<?php echo $res['id']; ?></p>
                        <p><b>Order Date :</b> <?php echo $res['date_added']; ?></p>
                        <hr>
                        <p><b>Name : </b><?php echo $res['name']; ?></p>
                        <p><b>Contact : </b><?php echo $res['phone']; ?></p>
                        <p><b>Email ID : </b><?php echo $res['email']; ?></p>
                        <p><b>Shipping Address : </b><?php echo $res['address']; ?>, <?php echo $res['city']; ?> , <?php echo $res_state['name']; ?> - <?php echo $res['zipcode']; ?></p>
                        <hr>
                        <p><b>Payable Amt : </b><i class="fa fa-rupee"></i> <?php echo $res['total']; ?></p>
                        <p><b>Payment Methods : </b> <?php if ($res['payment_type'] == 1) {
                                                            echo 'Cash on Delivery';
                                                        } elseif ($res['payment_type'] == 2) {
                                                            echo 'Online Payment';
                                                        } else {
                                                            echo 'Wallet';
                                                        } ?></p>
                    </div><br>
                </div>
                <div class="col-sm-12">
                    <table class="table cart-table table-responsive-xs">
                        <thead>
                            <tr class="table-head">
                                <th scope="col">product</th>
                                <th scope="col">product name</th>
                                <th scope="col">price</th>
                                <th scope="col">detail</th>
                            </tr>
                        </thead>
                        <tbody> <?php $sel_order_product = dbQuery("SELECT * FROM tabl_order_product WHERE order_id='" . $res['id'] . "'");
                                $shipping_total = 0;
                                $total = 0;
                                $grand_total = 0;
                                $i = 1;
                                while ($res_order_products = dbFetchAssoc($sel_order_product)) {
                                    $color = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_order_products['color'] . "'");
                                    $color_num = dbNumRows($color);
                                    $res_color = dbFetchAssoc($color);
                                    $size = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_order_products['size'] . "'");
                                    $size_num = dbNumRows($size);
                                    $res_size = dbFetchAssoc($size);
                                    $image = dbQuery("SELECT image FROM tabl_products WHERE id='" . $res_order_products['product_id'] . "'");
                                    $res_image = dbFetchAssoc($image);                                              ?>
                                <tr>
                                    <td>
                                        <a href="#"><img src="assets/images/products/<?php echo $res_image['image'] ?>" alt="product" class="img-fluid" style="width: 80px;height: 80px"></a>
                                    </td>
                                    <td><a href="#"><?php echo $res_order_products['name']; ?></a>
                                        <div class="mobile-cart-content row">
                                            <div class="col-xs-3">
                                                <div class="qty-box">
                                                    <div class="input-group">
                                                        <input type="text" name="quantity" class="form-control input-number" value="<?php echo $res_order_products['qty']; ?>">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-3">
                                                <h4 class="td-color">Rs. <?php echo $res_order_products['total']; ?></h4>
                                            </div>
                                            <div class="col-xs-3">
                                                <h2 class="td-color"><a href="#" class="icon"><i class="ti-close"></i></a></h2>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <h4>Rs. <?php echo $res_order_products['total']; ?></h4>
                                    </td>
                                    <td> <?php if ($color_num > 0) { ?> <span>Color: <?php echo $res_color['v_value']; ?></span><br /> <?php } ?> <?php if ($size_num > 0) { ?> <span>Size: <?php echo $res_size['v_value']; ?></span><br> <?php } ?>
                                        <span>Quantity: <?php echo $res_order_products['qty']; ?></span>
                                    </td>
                                </tr> <?php $total += $res_order_products['total'];
                                    } ?>
                        </tbody>
                    </table> <?php $shipping = dbQuery("SELECT sum(shipping_rate) as ttlshipping FROM `tabl_order_shipping` WHERE order_id='" . $res['id'] . "'");
                                $res_shipping = dbFetchAssoc($shipping);
                                if ($res_shipping['ttlshipping'] == "") {
                                    $shipping_rate = 0;
                                } else {
                                    $shipping_rate = $res_shipping['ttlshipping'];
                                }
                                $promo = dbQuery("SELECT sum(discount) as discount FROM `tabl_order_promo` WHERE order_id='" . $res['id'] . "'");
                                $res_promo = dbFetchAssoc($promo);
                                if ($res_promo['discount'] == "") {
                                    $discount = 0;
                                } else {
                                    $discount = $res_promo['discount'];
                                }                              ?> <div>
                        <p class="info"><strong>Total Price :</strong> Rs <?php echo  $total; ?></p>
                        <p class="info"><strong>Shipping Price :</strong> Rs <?php echo $shipping_rate; ?></p>
                        <p class="info"><strong>Discount :</strong> Rs -<?php echo $discount; ?></p>
                        <p class="info"><strong>Grand Total :</strong> Rs <?php echo ($total + $shipping_rate) - $discount ?></p>
                    </div>
                </div>
            </div>
            <!--<div class="row cart-buttons">
            <div class="col-12 pull-right"><a href="#" class="btn btn-normal btn-sm">show all orders</a></div>
        </div>-->
        </div>
    </section>
    <!--section end-->

    <!--footer-start-->

    <?php include('footer.php') ?>
    <!--footer-end-->


    <!-- tap to top -->
    <div class="tap-top">
        <div>
            <i class="fa fa-angle-double-up"></i>
        </div>
    </div>
    <!-- tap to top End -->



    <!-- latest jquery-->
    <script src="assets/js/jquery-3.3.1.min.js"></script>

    <!-- slick js-->
    <script src="assets/js/slick.js"></script>

    <!-- popper js-->
    <script src="assets/js/popper.min.js"></script>

    <!-- Timer js-->
    <script src="assets/js/menu.js"></script>

    <!-- Bootstrap js-->
    <script src="assets/js/bootstrap.js"></script>

    <!-- Bootstrap js-->
    <script src="assets/js/bootstrap-notify.min.js"></script>

    <!-- Theme js-->
    <script src="assets/js/script.js"></script>
    <script src="assets/js/slider-animat.js"></script>
    <script src="assets/js/timer.js"></script>
    <script src="assets/js/modal.js"></script>
</body>
<style>
    .mt-50 {
        margin-top: 50px;
    }

    .mb-20 {
        margin-bottom: 20px;
    }

    .info {
        font-size: 16px;
        margin-bottom: 2px;
    }
</style>

</html>