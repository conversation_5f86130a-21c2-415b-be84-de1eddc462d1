<?php
session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
include('lib/auth.php');
include('admin/inc/resize-class.php');
$page = 0;
$sub_page = 0;
$profile = 5;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
$date_time = date('Y-m-d H:i:s'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
  <title>Deva King 777</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <link rel="icon" href="./assets/img/logo/icon.png">
  <!--Google font-->
  <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

  <!--icon css-->
  <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
  <link rel="stylesheet" type="text/css" href="assets/css/themify.css">

  <!--Slick slider css-->
  <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
  <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">

  <!--Animate css-->
  <link rel="stylesheet" type="text/css" href="assets/css/animate.css">
  <!-- Bootstrap css -->
  <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">

  <!-- Theme css -->
  <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
  <!-- custom responsive css -->
  <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">
</head>

<body class="bg-light ">

  <!-- loader start -->
  <div class="loader-wrapper">
    <div>
      <!-- <img src="assets/images/loader.gif" alt="loader"> -->
      <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
    </div>
  </div>
  <!-- loader end -->

  <!--header start-->

  <?php include('header.php') ?>
  <!--header end-->

  <!-- breadcrumb start -->
  <div class="breadcrumb-main ">
    <div class="container">
      <div class="row">
        <div class="col">
          <div class="breadcrumb-contain">
            <div>
              <h2>My-Account</h2>
              <ul>
                <li><a href="#">home</a></li>
                <li><i class="fa fa-angle-double-right"></i></li>
                <li><a href="#">My-Order</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- breadcrumb End -->

  <!-- section start -->
  <section class="section-big-py-space bg-light">
    <div class="container">
      <div class="row">
        <?php include('account_sidebar.php'); ?>
        <div class="col-lg-9">
          <div class="dashboard-right">
            <div class="dashboard" style="padding-top: 1px;">
              <div class="box-account box-info">
                <div class="box-head">
                  <h2> Orders</h2>
                </div>
                <div class="row mt-20">
                  <div class="col-sm-12">
                    <div class="table-responsive">
                      <table class="table">
                        <thead>
                          <tr>
                            <th>Order</th>
                            <th>Date</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>View</th>
                            <th>Return </th>
                            <th>Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          <?php $sel = dbQuery("SELECT * FROM tabl_order WHERE customer_id='" . $_SESSION['customer_id'] . "' ORDER BY id DESC");
                          $i = 1;
                          while ($res = dbFetchAssoc($sel)) {
                            $total_item = dbQuery("SELECT * FROM tabl_order_product WHERE order_id='" . $res['id'] . "'");
                            $items = dbNumRows($total_item);
                            $date1 = $res['date_added'];
                            $date2 = $date_time;
                            $timestamp1 = strtotime($date1);
                            $timestamp2 = strtotime($date2);
                            $hour_cancel = abs($timestamp2 - $timestamp1) / (60 * 60);
                            $date_del1 = $res['deliver_date'];
                            $date_del2 = $date_time;
                            $timestamp1 = strtotime($date1);
                            $timestamp2 = strtotime($date2);
                            $hour_deliver = abs($timestamp2 - $timestamp1) / (60 * 60);                                   ?>
                            <tr>
                              <td>Deva King 777-<?php echo $res['id']; ?></td>
                              <td><i class="fa fa-calendar"></i> <?php echo date("M j, Y", strtotime($res['date_added']));; ?></td>
                              <td>Rs <?php echo $res['total']; ?> for <?php echo $items; ?> item </td>
                              <td><span class="success">
                                  <?php if ($res['order_status_id'] == 0) {
                                    echo 'Pending';
                                  } elseif ($res['order_status_id'] == 1) {
                                    echo 'Complete';
                                  } elseif ($res['order_status_id'] == 2) {
                                    echo 'Processed';
                                  } elseif ($res['order_status_id'] == 3) {
                                    echo 'Cancel';
                                  } elseif ($res['order_status_id'] == 4) {
                                    echo 'Delivered';
                                  } else {
                                    echo 'Return';
                                  } ?>
                                </span></td>
                              <td><a href="order-history.php?order_id=<?php echo $res['id']; ?>" class="view">
                                  <button class="btn btn-blue btn-sm"><i class="fa fa-eye"></i></button>
                                </a></td>
                              <td><?php
                                  if ($res['order_status_id'] == 4) {
                                    if ($hour_deliver < 24) { ?>
                                    <a href="javascript:void(0)" onClick="return_order(<?php echo $res['id'] . ',' . $i; ?>)" class="btn btn-normal return_button">Return</a>
                                <?php } else {
                                      echo 'N/A';
                                    }
                                  } elseif ($res['order_status_id'] == 5) {
                                    if ($res['return_request'] == 1) {
                                      echo 'Return Requested';
                                    } elseif ($res['return_request'] == 2) {
                                      echo 'Successfully Return';
                                    } else {
                                      echo 'N/A';
                                    }
                                  } else {
                                    echo 'N/A';
                                  } ?><div id="return_order_<?php echo $i; ?>"></div>
                              </td>
                              <td><?php
                                  if ($hour_cancel < 24 && $res['order_status_id'] == 2) { ?>
                                  <a href="javascript:void(0)" onClick="cancel_order(<?php echo $res['id'] . ',' . $i; ?>)" class="btn btn-normal cancel_button_<?php echo $i; ?>">Cancel</a>
                                <?php } else if ($res['order_status_id'] == 4) {
                                    echo 'N/A';
                                  } else {
                                    echo 'N/A';
                                  } ?> <div id="cancel_order_<?php echo $i; ?>"></div>
                              </td>
                            </tr>
                          <?php $i++;
                          } ?>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- section end -->

  <!--footer-start-->

  <?php include('footer.php') ?>
  <!--footer-end-->

  <!-- tap to top -->
  <div class="tap-top">
    <div> <i class="fa fa-angle-double-up"></i> </div>
  </div>
  <!-- tap to top End -->

  <!-- latest jquery-->
  <script src="assets/js/jquery-3.3.1.min.js"></script>

  <!-- slick js-->
  <script src="assets/js/slick.js"></script>

  <!-- popper js-->
  <script src="assets/js/popper.min.js"></script>

  <!-- Timer js-->
  <script src="assets/js/menu.js"></script>

  <!-- Bootstrap js-->
  <script src="assets/js/bootstrap.js"></script>

  <!-- Bootstrap js-->
  <script src="assets/js/bootstrap-notify.min.js"></script>

  <!-- Theme js-->
  <script src="assets/js/script.js"></script>
  <script src="assets/js/slider-animat.js"></script>
  <script src="assets/js/timer.js"></script>
  <script src="assets/js/modal.js"></script>
</body>
<style>
  .mt-20 {
    margin-top: 20px;
  }

  .btn-normal {
    font-size: 14px;
    padding: 10px 18px;
  }
</style>

</html>
<script>
  function cancel_order(order_id, row_id) {
    var retVal = confirm("Are you sure want to Cancel this order.");
    if (retVal == true) {
      $("#cancel_order_" + row_id).html('<img src="loader.gif" style="width: 10px;"></i> please wait...');
      $.ajax({
        url: 'ajax/cancel_order.php',
        type: 'post',
        data: {
          'order_id': order_id
        },
        success: function(data) {
          if (data == 1) {
            location.reload();
          }
        },
      });
    } else {
      return false;
    }
  }
</script>

<script>
  function return_order(order_id, row_id) {
    var retVal = confirm("Are you sure want to Return this order.");
    if (retVal == true) {
      $("#return_order_" + row_id).html('<img src="loader.gif" style="width: 10px;"></i> please wait...');
      $.ajax({
        url: 'ajax/return_order.php',
        type: 'post',
        data: {
          'order_id': order_id
        },
        success: function(data) {
          if (data == 1) {
            location.reload();
          }
        },
      });
    } else {
      return false;
    }
  }
</script>