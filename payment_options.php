<?php
session_start();
$session_id = session_id();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
//include('auth.php');
$page = 1;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
$cart_val = dbQuery("SELECT * FROM tabl_cart WHERE session_id='" . $session_id . "'");
$cart_num = dbNumRows($cart_val);
if ($cart_num == 0) {
    echo '<script>window.location.href="cart.php";</script>';
}

echo $_SESSION['order_total_amount'];


?>
<!DOCTYPE html>
<html lang="en">

<head>

    <title>Deva King 777</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="icon" href="./assets/img/logo/icon.png">
    <!--Google font-->
    <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <!--icon css-->
    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
    <link rel="stylesheet" type="text/css" href="assets/css/themify.css">
    <!--Slick slider css-->
    <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
    <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">
    <!--Animate css-->
    <link rel="stylesheet" type="text/css" href="assets/css/animate.css">
    <!-- Bootstrap css -->
    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">
    <!-- Theme css -->
    <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
    <!-- custom responsive css -->
    <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">
</head>

<body class="bg-light ">



    <!-- loader start -->

    <div class="loader-wrapper">
        <div>
            <!-- <img src="assets/images/loader.gif" alt="loader"> -->
            <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
        </div>
    </div>
    <!-- loader end -->



    <!--header start-->



    <?php include('header.php') ?>

    <!--header end-->



    <!-- breadcrumb start -->

    <div class="breadcrumb-main ">

        <div class="container">

            <div class="row">

                <div class="col">

                    <div class="breadcrumb-contain">

                        <div>

                            <h2>Payment Options</h2>

                            <ul>

                                <li><a href="#">home</a></li>

                                <li><i class="fa fa-angle-double-right"></i></li>

                                <li><a href="#">Payments</a></li>

                            </ul>

                        </div>

                    </div>

                </div>

            </div>

        </div>

    </div>

    <!-- breadcrumb End -->



    <!-- section start -->

    <section class="section-big-py-space bg-light">

        <div class="custom-container">
            <form id="checkout" method="POST">
                <div class="checkout-page contact-page">

                    <div class="checkout-form">
                        <div class="row">
                            <div class="col-lg-12 col-sm-12 col-xs-12">
                                <div class="checkout-details theme-form  section-big-mt-space">
                                    <div class="order-box">
                                        <div class="title-box">
                                            <div>TOTAL PAYABLE AMOUNT
                                                <span><?= number_format(isset($_SESSION['order_total_amount']) ? $_SESSION['order_total_amount'] : 0, 2) ?> RS</span>
                                            </div>
                                        </div>



                                        <div class="payment-box">
                                            <div class="upper-box">
                                                <div class="payment-options">
                                                    <ul>
                                                        <li>
                                                            <div class="radio-option">
                                                                <input type="radio" name="payment" id="payment-1"
                                                                    checked="checked" value="1">
                                                                <label for="payment-1">Cash On Delivery</label>
                                                            </div>
                                                        </li>
                                                        <li>

                                                            <div class="radio-option">
                                                                <input type="radio" name="payment" id="payment-2"
                                                                    value="2">
                                                                <label for="payment-2">Online Payments</label>
                                                            </div>

                                                        </li>

                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <input type="hidden" value="999" name="order_amount">
                                                <input type="hidden" value="100" name="order_shipping">

                                                <button type="submit" id="submit" class="btn-normal btn">Place
                                                    Order</button>
                                            </div>
                                        </div>

                                        <div class="checkout_loader" style="width:100%;"></div>

                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
            </form>
        </div>
    </section>

    <!-- section end -->





    <!--footer-start-->

    <?php include('footer.php') ?>

    <!--footer-end-->





    <!-- tap to top -->

    <div class="tap-top">

        <div>

            <i class="fa fa-angle-double-up"></i>

        </div>

    </div>

    <!-- tap to top End -->







    <!-- latest jquery-->

    <script src="assets/js/jquery-3.3.1.min.js"></script>



    <!-- slick js-->

    <script src="assets/js/slick.js"></script>



    <!-- popper js-->

    <script src="assets/js/popper.min.js"></script>



    <!-- Timer js-->

    <script src="assets/js/menu.js"></script>



    <!-- Bootstrap js-->

    <script src="assets/js/bootstrap.js"></script>



    <!-- Bootstrap js-->

    <script src="assets/js/bootstrap-notify.min.js"></script>



    <!-- Theme js-->

    <script src="assets/js/script.js"></script>

    <script src="assets/js/slider-animat.js"></script>

    <script src="assets/js/modal.js"></script>

</body>

</html>
<style>
    .option {
        font-size: 11px;
        font-weight: 600;
        color: #c51515;
    }
</style>
<script>
    $("#loginform").submit(function(e) {
        $("#submit").attr('disabled', true);
        $(".loader_login").html('<img src="loader.gif"></i> please wait...');
        $.ajax({
            url: 'ajax/customer_login.php',
            type: 'post',
            data: $("#loginform").serialize(),
            success: function(data) {
                if (data == 1) {
                    location.reload();
                } else {
                    $("#submit").attr('disabled', false);
                    $(".loader_login").html(
                        '<div class="alert alert-danger" role="alert">Either username or password are wrong!</div>'
                    );
                    setTimeout(function() {
                        $(".loader_login").html('');
                    }, 8000);

                }
            },
        });
        e.preventDefault(); // avoid to execute the actual submit of the form.
    });
</script>


<script>
    $("#checkout").submit(function(e) {
        $("#submit").attr('disabled', true);
        $(".checkout_loader").html(
            '<div class="alert alert-success" role="alert"><img src="loader.gif"></i> please wait...</div>');
        $.ajax({
            url: 'ajax/create_payment_option.php',
            type: 'post',
            data: $("#checkout").serialize(),
            success: function(data) {
                if (data == 1 || data == 3) {
                    window.location.href = 'thank-you.php';
                } else if (data == 2) {
                    // window.location.href = 'razorpay-php/pay.php';
                    window.location.href = 'phonepe_pay.php';
                } else {
                    $("#submit").attr('disabled', false);
                    $(".checkout_loader").html(
                        '<div class="alert alert-danger" role="alert">something is wrong!</div>');
                    setTimeout(function() {
                        $(".checkout_loader").html('');
                    }, 8000);

                }

            },
        });
        e.preventDefault(); // avoid to execute the actual submit of the form.
    });
</script>

<script>
    function isNumber(evt) {
        var iKeyCode = (evt.which) ? evt.which : evt.keyCode
        if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
            return false;

        return true;
    }

    function isDecimal(evt, obj) {

        var charCode = (evt.which) ? evt.which : event.keyCode
        var value = obj.value;
        var dotcontains = value.indexOf(".") != -1;
        if (dotcontains)
            if (charCode == 46) return false;
        if (charCode == 46) return true;
        if (charCode > 31 && (charCode < 48 || charCode > 57))
            return false;
        return true;
    }
</script>