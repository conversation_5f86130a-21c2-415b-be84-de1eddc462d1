<?php
session_start();
require_once("admin/lib/db_connection.php");

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');
$today = date('Y-m-d');

// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);



define('PHONEPE_MERCHANTID', 'M23Q88R5PNBM6');
define('PHONEPE_CLIENTID', 'SU2505161841055986790936');
define('PHONEPE_SALTKEY', '96ffacfe-aed6-44d4-9d3f-f9123c69c72c');
// define('PHONEPE_SALTKEY', '9d2785eb-e11b-4b08-b505-b3f0331c19ed');

// use Dwivedianuj9118\PhonePePaymentGateway\PhonePe;

// require './lib/PhonePG/autoload.php';

require_once 'vendor/autoload.php'; // Include Composer autoloader

use PhonePe\payments\v2\standardCheckout\StandardCheckoutClient;
// use Unit\payments\v2\standardCheckout\StandardCheckoutClientTest;

use PhonePe\Env;
use PhonePe\payments\v2\models\request\builders\StandardCheckoutPayRequestBuilder;
use PhonePe\common\exceptions\PhonePeException;

// $clientId = "SU2504101930509257958218"; // Replace with your Client ID

$clientId = PHONEPE_CLIENTID; // Replace with your Client ID
$clientVersion = 1;           // Replace with your Client Version
$clientSecret = PHONEPE_SALTKEY; // Replace with your Client Secret
$env = Env::PRODUCTION;  // Use Env::PRODUCTION for live environment

$client = StandardCheckoutClient::getInstance(
	$clientId,
	$clientVersion,
	$clientSecret,
	$env
);




$user_id = $_SESSION['user_id'];

$sel_user = dbQuery("SELECT * FROM tabl_user WHERE id='$user_id'");
$res_user = dbFetchAssoc($sel_user);

// $amount = 0;
// if (isset($_SESSION['amount']) && $_SESSION['amount'] != '') {
// 	$amount = $_SESSION['amount'];
// 	unset($_SESSION['amount']);

// 	if ($amount < MIN_RECHARGE) {
// 		echo '<script>alert("Please enter minimum rs. ' . MIN_RECHARGE . ' to deposit!");window.location.href="deposit.php"</script>';
// 		die();
// 	}
// } else {
// 	echo '<script>alert("Please enter a valid amount!");window.location.href="deposit.php"</script>';
// 	die();
// }

if (isset($_REQUEST['plan_id'])) {
	$plan_id = $_REQUEST['plan_id'];

	// Replace '~' with ',' and split the string
	$plan_range = str_replace('~', ',', $_REQUEST['plan_id']);
	// list($id, $price) = explode(',', $plan_range);
	$plan_data = explode(',', $plan_range);

	$plan_id = $plan_data[0];

	// Validate and cast to integers
	// $price = (int)$price;
} else {
	echo "<script>alert('Please select a valid recharge plan!');window.location.href = 'recharge.php'</script>";
}


$sel_plan = dbQuery("SELECT * FROM tabl_recharge_plans WHERE id='$plan_id'");
if (!$res_plan = dbFetchAssoc($sel_plan)) {
	echo "<script>alert('Please select a valid recharge plan!');window.location.href = 'recharge.php'</script>";
}



// $user_id = $_SESSION['user_id'];

$price = $res_plan['price'];
$amount = $res_plan['amount'];
// $email = $user['email'];

$today = date("Y-m-d H:i:s");
$site_name = SITE;



// if (isset($_SESSION['deposit_id']) && $_SESSION['deposit_id'] != '') {
// 	$deposit_id = $_SESSION['deposit_id'];
// 	unset($_SESSION['deposit_id']);
// } else {
// 	echo '<script>alert("Please select a valid payment method!");window.location.href="deposit.php"</script>';
// 	die();
// }



$name = $res_user['fname'] . ' ' . $res_user['lname'];
$phone = $res_user['phone'];
$email = $res_user['email'];

// $today = date("Y-m-d H:i:s");


// dbQuery("INSERT INTO `tabl_notification` SET `user_id`='" . $user_id . "', `type`='recharge', `title`='apply for recharge', `description`='Your recharge request has been submitted', data='$amount', `status`='0', `date`='$date'");

// echo $deposits_id;

// We create an razorpay order using orders api
// Docs: https://docs.razorpay.com/docs/orders
//




$txn_id = (string) rand(100000, 999999);

// $sel_pay = dbQuery("INSERT INTO `tabl_payment` SET `agent_id`='$user_id', `row_id`='$row_id', `pay_type`='$pay_type',`email`='$email',`phone`='$phone',`name`='$name',`amount`='$amount',`txn_id`='$txn_id',`status`='0', `date`='$date'");
// $deposits_id = dbInsertId();

// $deposit = dbQuery("INSERT INTO `tabl_deposits`(`ref_num`, `amount`, `email`, `status`, `user_id`, `deposit_id`, `date`) VALUES('$txn_id', '$amount', '$email', '0','$user_id', '$deposit_id', '$today')");
$deposit = dbQuery("INSERT INTO tabl_deposits SET user_id='" . $user_id . "', price='" . $price . "', amount='" . $amount . "',phone='" . $phone . "',email='" . $email . "', ref_num='', status='0', date='" . $date . "'");

$deposit_id = dbInsertId();

// Define cookie expiry time (30 days)
$cookie_expire = time() + (86400 * 30); // 30 days
$cookie_path = "/";

// Set specific user-related cookies individually
setcookie('user_id', $_SESSION['user_id'] ?? '', $cookie_expire, $cookie_path);
setcookie('user', $_SESSION['user'] ?? '', $cookie_expire, $cookie_path);
setcookie('name', $_SESSION['name'] ?? '', $cookie_expire, $cookie_path);
setcookie('phone', $_SESSION['phone'] ?? '', $cookie_expire, $cookie_path);

//  $price

$convertedPrice = $price * 100;
$merchantOrderId = 'Order' . mt_rand(10000000, 999999999);
$merchantTransactionId = 'TXIDX' . uniqid();

// $config = new PhonePe(PHONEPE_MERCHANTID, PHONEPE_SALTKEY, 1);



$amount_to_pay = (int)$convertedPrice;
// $redirectUrl = "http://127.0.0.9/devaking777_site/phonepe_thank_you.php?order_id=" . $merchantOrderId;
// $callbackUrl = "http://127.0.0.9/devaking777_site/phonepe_thank_you.php?order_id=" . $merchantOrderId;

$redirectUrl = $base_url . 'phonepe_thank_you.php?order_id=' . $merchantOrderId;
$callbackUrl = $base_url . 'phonepe_thank_you.php?order_id=' . $merchantOrderId;
$mobileNumber = $phone;

// $mode = "UAT";
// $mode = "PRO";

// $data = $config->PaymentCall($merchantTransactionId, $merchantOrderId, $amount, $redirectUrl, $callbackUrl, $mobileNumber, $mode);

$message = "New Order with Order ID: " . $deposit_id . " has been successfully paid.";

$payRequest = StandardCheckoutPayRequestBuilder::builder()
	->merchantOrderId($merchantOrderId)
	->amount($amount_to_pay)
	->redirectUrl($redirectUrl)
	->message($message)  //Optional Message
	->build();

// print_r($data);

try {
	$payResponse = $client->pay($payRequest);

	print_r($payResponse);

	// Handle the response
	if ($payResponse->getState() === "PENDING") {


		// dbQuery("UPDATE tabl_bookings SET payment_status='0', txn_id='" . $merchantTransactionId . "', `order_id`='$merchantOrderId' WHERE id='$booking_id'");
		// dbQuery("UPDATE tabl_bookings SET payment_status='0', `order_id`='$merchantOrderId' WHERE id='$booking_id'");

		// dbQuery("UPDATE tabl_order SET response_id='$merchantOrderId' WHERE id='" . $order_id . "'");

		$sel_pay = dbQuery("UPDATE `tabl_deposits` SET ref_num='" . $merchantOrderId . "' WHERE id='$deposit_id'");

		// $deposit = dbQuery("INSERT INTO tabl_deposits SET user_id='" . $user_id . "', price='" . $price . "', amount='" . $amount . "',phone='" . $phone . "',email='" . $email . "', ref_num='" . $merchantOrderId . "', status='0', date='" . $date . "'");
		// $deposit_id = dbInsertId();

		// Redirect the user to the PhonePe payment page
		// header("Location: " . $payResponse->getRedirectUrl());
		echo '<script>window.location.href="' . $payResponse->getRedirectUrl() . '"</script>';
		exit();
	} else {
		// Handle the error (e.g., display an error message)
		echo "Payment initiation failed: " . $payResponse->getState();
		echo '<script>alert("Payment failed to initiate!");window.location.href="recharge.php"</script>';
	}
} catch (PhonePe\common\exceptions\PhonePeException $e) {
	// Handle exceptions (e.g., log the error)
	echo "Error initiating payment: " . $e->getMessage();
	echo '<script>alert("Payment failed to initiate!");window.location.href="recharge.php"</script>';
}

// if ($result['status'] == true) {
// 	$order_id = $result['data']['order_id'];
// 	// $sel_pay = dbQuery("UPDATE `tabl_payment` SET order_id='$order_id', `status`='1' WHERE `txn_id`='$txn_id'");

// 	$sel_pay = dbQuery("UPDATE `tabl_deposits` SET order_id='$order_id' WHERE `ref_num`='$txn_id'");

// 	echo '<script>location.href="' . $result['data']['payment_url'] . '"</script>';
// 	exit();
// }

// $sel_pay = dbQuery("UPDATE `tabl_payment` SET `status`='2' WHERE `txn_id`='$txn_id'");

// echo '<div class="alert alert-danger">' . $result['msg'] . '</div>';

// echo '<script>location.href="index.php"</script>';
exit();
