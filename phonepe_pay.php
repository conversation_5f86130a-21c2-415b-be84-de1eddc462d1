<?php
@session_start();
include('admin/lib/db_connection.php');
// include('auth.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');
$today = date('Y-m-d');

// Fetch user details
$sel_user = dbQuery("SELECT * FROM tabl_customer WHERE id='" . intval(@$_SESSION['customer_id']) . "'");

if (!$res_user = dbFetchAssoc($sel_user)) {
  echo '<script>alert("Please select a valid user!");window.location.href=".index.php"</script>';
  die();
}

// Define constants
// define('PHONEPE_MERCHANTID', 'PGTESTPAYUAT');
// define('PHONEPE_SALTKEY', 'e32e43ec-95e3-47e9-a0de-4a0d901441c8');
// define('PHONEPE_SALTKEY', 'MjdhZjk3Y2EtOTEyMy00Mzk1LTk4OTgtYWE3NmRhNGE2MzA2');

// Define constants
// aao hotel
define('PHONEPE_MERCHANTID', 'M23Q88R5PNBM6');
define('PHONEPE_CLIENTID', 'SU2505161841055986790936');
define('PHONEPE_SALTKEY', '96ffacfe-aed6-44d4-9d3f-f9123c69c72c');
// define('PHONEPE_SALTKEY', '9d2785eb-e11b-4b08-b505-b3f0331c19ed');

// use Dwivedianuj9118\PhonePePaymentGateway\PhonePe;

// require './lib/PhonePG/autoload.php';

require_once 'vendor/autoload.php'; // Include Composer autoloader

use PhonePe\payments\v2\standardCheckout\StandardCheckoutClient;
// use Unit\payments\v2\standardCheckout\StandardCheckoutClientTest;

use PhonePe\Env;
use PhonePe\payments\v2\models\request\builders\StandardCheckoutPayRequestBuilder;
use PhonePe\common\exceptions\PhonePeException;

// $clientId = "SU2504101930509257958218"; // Replace with your Client ID

$clientId = PHONEPE_CLIENTID; // Replace with your Client ID
$clientVersion = 1;           // Replace with your Client Version
$clientSecret = PHONEPE_SALTKEY; // Replace with your Client Secret
$env = Env::PRODUCTION;  // Use Env::PRODUCTION for live environment

$client = StandardCheckoutClient::getInstance(
  $clientId,
  $clientVersion,
  $clientSecret,
  $env
);




if (isset($_SESSION['receipt_id']) && $_SESSION['receipt_id'] != '') {
  $order_id = $_SESSION['receipt_id'];
  $product_name = 'New Order# ' . $_SESSION['receipt_id'] . '';
  $name = $_SESSION['customer_name'];
  $phone = $_SESSION['customer_phone'];
  $email = $_SESSION['customer_email'];
  $new_price = $_SESSION['order_total_amount'];



  $customer_id = $_SESSION['customer_id'];
  $price = floatval($new_price);
  // $phone = $res_user['phone'];
  // $UserId = $res_user['id'];

  // Define cookie expiry time (30 days)
  $cookie_expire = time() + (86400 * 30); // 30 days
  $cookie_path = "/";

  // Set specific user-related cookies individually
  setcookie('customer_id', $_SESSION['customer_id'] ?? '', $cookie_expire, $cookie_path);
  setcookie('customer_name', $_SESSION['customer_name'] ?? '', $cookie_expire, $cookie_path);
  setcookie('customer_email', $_SESSION['customer_email'] ?? '', $cookie_expire, $cookie_path);
  setcookie('customer_phone', $_SESSION['customer_phone'] ?? '', $cookie_expire, $cookie_path);

  // Loop through session and store simple scalar values only
  foreach ($_SESSION as $key => $value) {
    // Avoid duplicating keys already set above
    if (in_array($key, ['customer_id', 'customer_name', 'customer_email', 'customer_phone'])) {
      continue;
    }

    // Only store scalar (string/int/float/bool) values in cookies
    if (is_scalar($value)) {
      setcookie($key, $value, $cookie_expire, $cookie_path);
    }

    // Optionally: store arrays or objects like `shipping_order` as JSON (with caution)
    elseif ($key === 'shipping_order') {
      // Compress and encode shipping_order
      $json_shipping_order = json_encode($value);

      // Only store if it's under 4000 bytes (cookie limit)
      if (strlen($json_shipping_order) < 4000) {
        setcookie($key, $json_shipping_order, $cookie_expire, $cookie_path);
      }
    }
  }

  // $deposit = dbQuery("INSERT INTO `tabl_deposits`(`amount`, `email`, `status`, `customer_id`, `date`) VALUES('$amount', '" . $_SESSION['user_email'] . "', '0', '$customer_id', '$date')");
  // $deposit_id = dbInsertId();

  $convertedPrice = $price * 100;
  $merchantOrderId = 'Order' . mt_rand(10000000, 999999999);
  $merchantTransactionId = 'TXIDX' . uniqid();

  // $config = new PhonePe(PHONEPE_MERCHANTID, PHONEPE_SALTKEY, 1);



  $amount = (int)$convertedPrice;
  // $redirectUrl = "http://*********/devaking777_site/phonepe_thank_you.php";
  // $callbackUrl = "http://*********/devaking777_site/phonepe_thank_you.php";

  // $redirectUrl = "https://devaking777.com/phonepe_thank_you.php?order_id=" . $merchantOrderId;
  // $callbackUrl = "https://devaking777.com/phonepe_thank_you.php?order_id=" . $merchantOrderId;

  $redirectUrl = $base_url . "phonepe_thank_you.php?order_id=" . $merchantOrderId;
  $callbackUrl = $base_url . "phonepe_thank_you.php?order_id=" . $merchantOrderId;
  $mobileNumber = $phone;
  // $mode = "UAT";
  // $mode = "PRO";

  // $data = $config->PaymentCall($merchantTransactionId, $merchantOrderId, $amount, $redirectUrl, $callbackUrl, $mobileNumber, $mode);

  $message = "New Order with Order ID: " . $order_id . " has been successfully paid.";

  $payRequest = StandardCheckoutPayRequestBuilder::builder()
    ->merchantOrderId($merchantOrderId)
    ->amount($amount)
    ->redirectUrl($redirectUrl)
    ->message($message)  //Optional Message
    ->build();

  // print_r($data);

  try {
    $payResponse = $client->pay($payRequest);

    print_r($payResponse);

    // Handle the response
    if ($payResponse->getState() === "PENDING") {


      // dbQuery("UPDATE tabl_bookings SET payment_status='0', txn_id='" . $merchantTransactionId . "', `order_id`='$merchantOrderId' WHERE id='$booking_id'");
      // dbQuery("UPDATE tabl_bookings SET payment_status='0', `order_id`='$merchantOrderId' WHERE id='$booking_id'");

      dbQuery("UPDATE tabl_order SET response_id='$merchantOrderId' WHERE id='" . $order_id . "'");


      // Redirect the user to the PhonePe payment page
      // header("Location: " . $payResponse->getRedirectUrl());
      echo '<script>window.location.href="' . $payResponse->getRedirectUrl() . '"</script>';
      exit();
    } else {
      // Handle the error (e.g., display an error message)
      echo "Payment initiation failed: " . $payResponse->getState();
      echo '<script>alert("Payment failed to initiate!");window.location.href="index.php"</script>';
    }
  } catch (PhonePe\common\exceptions\PhonePeException $e) {
    // Handle exceptions (e.g., log the error)
    echo "Error initiating payment: " . $e->getMessage();
    echo '<script>alert("Payment failed to initiate!");window.location.href="index.php"</script>';
  }
} else {
  // echo '<script>alert("Please enter billing details!");window.location.href="index.php"</script>';
  echo '<script>alert("Please select a valid order id!");window.location.href="index.php"</script>';
  die();
}
