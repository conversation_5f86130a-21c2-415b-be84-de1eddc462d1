<?php
@session_start();
// session_start();

include('admin/lib/db_connection.php');

foreach ($_COOKIE as $key => $value) {
    // Skip the PHP session cookie itself
    if ($key === 'PHPSESSID') {
        continue;
    }

    // If session value is not already set, sync it from cookie
    if (!isset($_SESSION[$key])) {
        // Decode JSON if this is a known complex structure like 'shipping_order'
        if ($key === 'shipping_order') {
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $_SESSION[$key] = $decoded;
            }
        } else {
            // Simple scalar value
            $_SESSION[$key] = $value;
        }

        // Optional: delete cookie after restoring to session
        // setcookie($key, '', time() - 3600, "/"); // Uncomment to clean up
    }
}


// include('admin/lib/db_connection.php');
// include('auth.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');
// $sel_user = dbQuery("SELECT * FROM tabl_user WHERE id='" . @$_SESSION['user_id'] . "'");
// $res_user = dbFetchAssoc($sel_user);


require_once "vendor/autoload.php"; // Include Composer autoloader

use PhonePe\payments\v2\standardCheckout\StandardCheckoutClient;
use PhonePe\Env;

define('PHONEPE_MERCHANTID', 'M23Q88R5PNBM6');
define('PHONEPE_CLIENTID', 'SU2505161841055986790936');
define('PHONEPE_SALTKEY', '96ffacfe-aed6-44d4-9d3f-f9123c69c72c');

$clientId = PHONEPE_CLIENTID; // Replace with your Client ID
$clientVersion = 1; // Replace with your Client Version
$clientSecret = PHONEPE_SALTKEY; // Replace with your Client Secret
$env = Env::PRODUCTION;

$client = StandardCheckoutClient::getInstance(
    $clientId,
    $clientVersion,
    $clientSecret,
    $env
);

if (!isset($_REQUEST['order_id']) || $_REQUEST['order_id'] == '') {
}
$merchantOrderId = $_REQUEST['order_id']; // Fix: Use $_REQUEST instead of $_POST

// $merchantOrderId = $order_id; // Replace with the order ID you want to check

try {

    // $query = dbQuery("SELECT * FROM tabl_order WHERE response_id='$order_id' AND payment_status!='1'");
    $query = dbQuery("SELECT * FROM tabl_order WHERE response_id='$merchantOrderId' AND order_status_id='0'");

    // echo "SELECT * FROM tabl_order WHERE id='$booking_id' AND payment_status!='1'";

    if (!$res_order = dbFetchAssoc($query)) {
        echo '<script>alert("Please select a valid order id!");window.location.href="/orders/"</script>';
        die();
    }

    $statusCheckResponse = $client->getOrderStatus($merchantOrderId, true);

    // Process the status check response
    // echo "Order ID: " . $statusCheckResponse->getMerchantId() . "\n";
    // echo "Transaction ID: " . $statusCheckResponse->getTransactionId() . "\n";
    // echo "State: " . $statusCheckResponse->getState() . "\n";
    // echo "Amount: " . $statusCheckResponse->getAmount() . "\n";

    $state = $statusCheckResponse->getState(); // "COMPLETED" or "FAILED"

    // $transactionId = $statusCheckResponse->getTransactionId() ?? '';
    // $upi_txn_id = $payload['paymentDetails'][0]['splitInstruments'][0]['rail']['upiTransactionId'] ?? '';
    // $timestamp = $payload['paymentDetails'][0]['timestamp'] ?? null;

    // $net_payable_amount = $res_order['net_payable_amount'];
    // $fee_amount = $net_payable_amount * 0.15;
    // $fee_tax = $fee_amount * 0.18;

    // $amount = round($fee_amount + $fee_tax, 2);

    // $transaction_amount = $statusCheckResponse->getAmount() ?? 0;

    // $transaction_amount = round($fee_amount + $fee_tax, 2);


    if ($state === 'COMPLETED') {
        // Payment Success
        // dbQuery("UPDATE `tabl_order` SET order_status_id=2, `txn_date`='$formattedDate', `upi_txn_id`='$upi_txn_id', `txn_id`='$transactionId' WHERE `order_id`='$merchantOrderId'");
        // dbQuery("UPDATE `tabl_order` SET order_status_id=2, `booking_status`='confirmed', `txn_id`='$transactionId' WHERE `order_id`='$merchantOrderId'");
        // dbQuery("UPDATE `tabl_order` SET order_status_id=2,`payment_type`='3',transaction_id='" . $_POST['razorpay_payment_id'] . "' WHERE `response_id`='$merchantOrderId'");

        dbQuery("UPDATE `tabl_order` SET order_status_id=1,`payment_type`='3' WHERE `response_id`='$merchantOrderId'");

        $sel_order2 = dbQuery("SELECT * FROM tabl_order WHERE response_id ='$merchantOrderId'");
        if ($res_order2 = dbFetchAssoc($sel_order2)) {
            //     $net_payable_amount = $res_order2['net_payable_amount']; // Fix: Use booking result, not $res_user

            //     $balance_amount = $net_payable_amount - $transaction_amount;

            //     dbQuery("UPDATE `tabl_order` SET `paid_amount`='$transaction_amount', `balance_amount`='$balance_amount' WHERE `response_id`='$merchantOrderId'");

            $order_id = $res_order2['id'];
        }

        $pay_state = 1;
        $echo = '<div class="alert alert-success">Transaction Status: COMPLETED</div>';
    } else {
        // Payment Failed
        dbQuery("UPDATE `tabl_order` SET `order_status_id`='3' WHERE `response_id`='$merchantOrderId'");
        $pay_state = 0;
        $echo = '<div class="alert alert-danger">Transaction Status: FAILED</div>';
    }


    // Optional: Fetch booking/user for session
    $sel_order = dbQuery("SELECT * FROM tabl_order WHERE response_id ='$merchantOrderId'");
    if ($res_order = dbFetchAssoc($sel_order)) {
        $_SESSION['user_id'] = $res_order['customer_id']; // Fix: Use booking result, not $res_user
    }

    unset($_SESSION['receipt_id']);

    // You can access further details from $statusCheckResponse->getPaymentInstrument(), $statusCheckResponse->getPayResponseCode()
} catch (\PhonePe\common\exceptions\PhonePeException $e) {
    // Handle exceptions (e.g., log the error)
    echo "Error checking order status: " . $e->getMessage();
    $pay_state = 0;
}
?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Lifimix - Payment Success</title>

    <link href="https://fonts.googleapis.com/css?family=Nunito+Sans:400,400i,700,900&display=swap" rel="stylesheet" />

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"
        integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">
</head>
<style>
    body {
        text-align: center;
        padding: 40px 0;
        background: #ebf0f5;
    }

    h1 {
        font-family: "Nunito Sans", "Helvetica Neue", sans-serif;
        font-weight: 900;
        font-size: 40px;
        margin-bottom: 10px;
    }

    p {
        color: #404f5e;
        font-family: "Nunito Sans", "Helvetica Neue", sans-serif;
        font-size: 16px;
        margin: 0;
        margin-bottom: 5px;
    }

    i {
        font-size: 100px;
        line-height: 200px;
        margin-left: -15px;
    }

    .card {
        background: white;
        padding: 60px;
        border-radius: 4px;
        box-shadow: 0 2px 3px #c8d0d8;
        display: inline-block;
        margin: 0 auto;
    }
</style>

<body>

    <?php if ($pay_state) { ?>
        <div class="card">
            <div style="border-radius: 200px;height: 200px;width: 200px;background: #f8faf5;margin: 0 auto;">
                <i class="checkmark">✓</i>
            </div>
            <h1 style="color: #88b04b;" style="color: #9abc66;">Success</h1>
            <p>
                Transaction ID: <?= $merchantOrderId; ?>
            </p>
            <p>
                Your payment successfully done, <br>
                <!-- admin will verify your details and send confirmation on registered email id! -->
            </p>

            <a href="/orders/" class="btn btn-success mt-3">Back to home</a>
        </div>

    <?php } else { ?>
        <div class="card">
            <div style="border-radius: 200px;height: 200px;width: 200px;background: #faf5f5;margin: 0 auto;">
                <i class="checkmark" style="color: #dc3545;">✗</i>
            </div>
            <h1 style="color: #dc3545;">Failed</h1>
            <p>
                Transaction ID: <?= $merchantOrderId; ?>
            </p>
            <p>
                <!-- <?= $result['data']['status']; ?>, <br> please try again later! -->
                Your payment is failed, <br> please try again later!
            </p>

            <a href="/orders/" class="btn btn-danger mt-3">Back to home</a>
        </div>
    <?php } ?>

    <!-- <div class="row">
        <div class="col-md-8 mb-2">
            <h2>Response</h2>
            <p>Payment Gateway - Test Response</p>
            <?php echo $echo;
            // show table of response
            if (isset($txn_data)) {
                echo '<table class="table table-bordered">
					<thead>
					  <tr>
						<th>Key</th>
						<th>Value</th>
					  </tr>
					</thead>
					<tbody>';
                foreach ($txn_data as $key => $value) {
                    echo '<tr>
						<td>' . $key . '</td>
						<td>' . @$value . '</td>
					  </tr>';
                }
                echo '</tbody>
				  </table>';
            }
            ?>

        </div>
    </div> -->

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js"
        integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj"
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-Fy6S3B9q64WdZWQUiU+q4/2Lc9npb8tCaSX9FK7E8HnRr0Jz8D6OP9dO5Vg3Q9ct"
        crossorigin="anonymous"></script>

    <script>
        // URL to redirect to
        var url = "<?= $base_url; ?>";

        // Function to redirect after 5 seconds
        setTimeout(function() {
            // window.location.href = url;
        }, 5000); // 5000 milliseconds = 5 seconds
    </script>
</body>

</html>