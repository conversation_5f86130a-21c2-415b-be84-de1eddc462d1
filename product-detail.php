<?php
session_start();
include ('admin/lib/db_connection.php');
include ('admin/lib/get_functions.php');
//include('auth.php');
$page = 1;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>Deva King 777</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="icon" href="./assets/img/logo/icon.png">
    <!--Google font-->
    <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

    <!--icon css-->

    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">

    <link rel="stylesheet" type="text/css" href="assets/css/themify.css">



    <!--Slick slider css-->

    <link rel="stylesheet" type="text/css" href="assets/css/slick.css">

    <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">



    <!--Animate css-->

    <link rel="stylesheet" type="text/css" href="assets/css/animate.css">

    <!-- Bootstrap css -->

    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">



    <!-- Theme css -->

    <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
    <!-- custom responsive css -->
    <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">
    <style>
        .slick-prev,
        .slick-next {
            display: none !important;
        }
    </style>


    <style>
        .likeicon {
            position: absolute;
            z-index: 8;
            top: 0;
            right: 0;
            text-align: right;
            padding: 11px;
            color: brown;
            font-size: 20px;
        }
    </style>
</head>

<body class="bg-light ">



    <!-- loader start -->
    <div class="loader-wrapper">
        <div>
            <!-- <img src="assets/images/loader.gif" alt="loader"> -->
            <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
        </div>
    </div>

    <!-- loader end -->



    <!--header start-->



    <?php include ('header.php') ?>

    <!--header end-->





    <!-- section start -->

    <section class="section-big-pt-space bg-light mtb-50">
        <?php $sel = dbQuery("SELECT * FROM tabl_products WHERE id='" . $_REQUEST['productID'] . "'");
        $res = dbFetchAssoc($sel);
        $off = (($res['old_price'] - $res['price']) / $res['old_price']) * 100;
        $off = round($off, 0);

        if (isset($_SESSION['user_id'])) {
            $sel_like = dbQuery("SELECT * FROM tabl_likes WHERE product_id='" . $_REQUEST['productID'] . "' AND user_id = '" . $_SESSION['user_id'] . "'");
            $num_like = dbNumRows($sel_like);

            $is_liked = $num_like > 0 ? "fa-solid" : "fa-regular";
        } else {
            $is_liked = 'fa-regular';
        }

        ?>
        <div class="collection-wrapper">
            <div class="custom-container">
                <div class="row">
                    <div class="col-lg-5">
                        <div class="product-slick no-arrow">
                            <div><img src="assets/images/products/<?php echo $res['image'] ?>" alt=""
                                    class="img-fluid  image_zoom_cls-0"></div>
                            <?php $sel_big = dbQuery("SELECT * FROM tabl_product_images WHERE p_id='" . $_REQUEST['productID'] . "'");
                            $i = 1;
                            while ($res_big = dbFetchAssoc($sel_big)) { ?>
                                <div><img src="assets/images/products/<?php echo $res_big['image'] ?>" alt=""
                                        class="img-fluid  image_zoom_cls-<?php echo $i; ?>"></div>
                                <?php $i++;
                            } ?>
                        </div>

                        <div class="row">
                            <div class="col-12 p-0">
                                <div class="slider-nav">
                                    <div><img src="assets/images/products/<?php echo $res['image'] ?>" alt=""
                                            class="img-fluid  image_zoom_cls-0"></div>

                                    <?php $sel_small = dbQuery("SELECT * FROM tabl_product_images WHERE p_id='" . $_REQUEST['productID'] . "'");
                                    $i = 1;
                                    while ($res_small = dbFetchAssoc($sel_small)) { ?>
                                        <div><img src="assets/images/products/<?php echo $res_small['image'] ?>" alt=""
                                                class="img-fluid  image_zoom_cls-<?php echo $i; ?>"></div>
                                        <?php $i++;
                                    } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-7 rtl-text">
                        <div class="product-right">
                            <h2><?php echo $res['name']; ?></h2>
                            <p>Product Id : <?php echo $res['product_code']; ?> </p><br>

                            <div class="likeicon"
                                onclick="likeProduct(event, <?php echo $_REQUEST['productID']; ?>, 1)">
                                <i class="<?= $is_liked; ?> fa-heart"
                                    id="like_icon_1_<?= $_REQUEST['productID']; ?>"></i>
                            </div>
                            <div class="share-btn" onclick="copyLinkToClipboard('<?= $_REQUEST['productID']; ?>')">
                                <i class="fa-solid fa-share"></i>
                            </div>


                            <h4><del>Rs <?php echo $res['old_price'] ?></del><span class="off-text"><?php echo $off ?>%
                                    off</span></h4>
                            <h3 class="price-text">Rs <?php echo $res['price'] ?></h3>
                            <?php $sel_option_color = dbQuery("SELECT `tabl_product_options`.*,tabl_product_variation_value.v_value,color FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_options.v_id=tabl_product_variation_value.id WHERE tabl_product_options.p_id='" . $_REQUEST['productID'] . "' AND tabl_product_options.v_type=1");
                            $color_num = dbNumRows($sel_option_color);
                            if ($color_num > 0) {
                                ?>
                                <h6 class="product-title size-text">select Color</h6><br>
                                <ul class="color-variant">
                                    <?php
                                    while ($res_color = dbFetchAssoc($sel_option_color)) {
                                        ?>
                                        <li class="bg-light0" data-id="<?php echo $res_color['id'] ?>"
                                            style="background-color:<?php echo $res_color['color']; ?>"></li>
                                    <?php } ?>
                                </ul>
                                <input type="hidden" name="color_option" id="color_option" value="">
                                <div class="color_msg"></div>

                            <?php } else { ?>
                                <input type="hidden" name="color_option" id="color_option" value="0">
                            <?php } ?>


                            <div class="des_info" itemprop="description">
                                <?php echo $res['short_description']; ?>
                            </div>
                            <?php
                            $sel_offer = dbQuery("SELECT * FROM tabl_promo_code WHERE start_date<='" . $date . "' AND end_date>='" . $date . "' AND FIND_IN_SET(" . $res['sub_cat_id'] . ",sub_cat_id)");
                            $offer_num = dbNumRows($sel_offer);
                            if ($offer_num > 0) {
                                ?>
                                <div class="offers_pdp">
                                    <div class="head_div">
                                        <span class="h1" id="new_offer_count"><i class="fa fa-tag"></i>
                                            <?php echo $offer_num; ?> offers Available for you</span>
                                    </div>
                                    <ul>
                                        <?php while ($res_offer = dbFetchAssoc($sel_offer)) { ?>
                                            <li class="pdp_offrs mtb-20" id="first_promo">
                                                <div class="coupons_code">
                                                    <span><?php echo $res_offer['name']; ?></span>
                                                </div>
                                            </li>
                                        <?php } ?>
                                    </ul>
                                </div>
                            <?php } ?>

                            <?php $sel_option_size = dbQuery("SELECT `tabl_product_options`.*,tabl_product_variation_value.v_value,color FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_options.v_id=tabl_product_variation_value.id WHERE tabl_product_options.p_id='" . $_REQUEST['productID'] . "' AND tabl_product_options.v_type=2");
                            $size_num = dbNumRows($sel_option_size); ?>
                            <?php if ($size_num > 0) { ?>
                                <div class="product-description border-product">
                                    <h6 class="product-title size-text">select size</h6>

                                    <div class="size-box">

                                        <ul>
                                            <?php
                                            while ($res_size = dbFetchAssoc($sel_option_size)) {
                                                ?>
                                                <li class="size-option"
                                                    data-id="<?php echo $res_size['id'] . '~~~' . $res_size['price'] ?>"><a
                                                        href="javascript:void(0)"><?php echo $res_size['v_value']; ?></a></li>
                                            <?php } ?>
                                        </ul>
                                        <input type="hidden" name="size_option" id="size_option" value="">
                                        <div class="size_msg"></div>
                                    </div>
                                </div>
                            <?php } else { ?>
                                <input type="hidden" name="size_option" id="size_option" value="0">
                            <?php } ?>
                            <div class="product-buttons">
                                <input type="hidden" name="product_id" id="product_id"
                                    value="<?php echo $_REQUEST['productID']; ?>">
                                <input type="hidden" name="price" id="price" value="<?php echo $res['price']; ?>">
                                <input type="hidden" name="sub_cat_id" id="sub_cat_id"
                                    value="<?php echo $res['sub_cat_id']; ?>">
                                <a href="javascript:void(0)" onclick="add_to_cart()" class="btn btn-normal add-to-cart"
                                    id="add-to-cart">add to cart</a>


                                <a href="javascript:void(0)" onclick="buy_now()" id="buy-now" class="btn btn-normal">buy
                                    now</a>

                                <?php
                                if (isset($_SESSION['customer_id'])) {

                                    ?>
                                    <a href="javascript:void(0)" onclick="add_to_wishlist()" id="add_to_wishlist"
                                        class="btn btn-normal">Add To WishList</a>
                                    <?php
                                } ?>

                            </div>



                            <div class="pin_location">
                                <div class="check_pincode">
                                    <div id="pd_pin" class="theme-form">
                                        <div class="form-row">
                                            <form id="check_pincode" method="post">
                                                <input type="text" name="pincode" id="pincode" class="location mr-20"
                                                    placeholder="Enter pincode for delivery"
                                                    onkeypress="return isNumber(event,this)" maxlength="6" required>
                                                <input type="submit" id="submit" name="button" value="Check"
                                                    class="btn btn-rounded btn-outline">
                                            </form>
                                        </div>
                                        <div class="pincode_loader"></div>
                                    </div>
                                </div>


                                <div class="status_i">

                                    <ul id="delDetails">

                                        <li><i class="fa fa-check"></i> COD <span id="iscod">Available</span></li>

                                        <li id="shippingcharge"><i class="fa fa-check"></i> Shipping: <span
                                                id="shipcharge">Rs. <?php echo $res['shipping_price'] ?></span></li>

                                        <li id="del_time"><i class="fa fa-check"></i> Delivered <span
                                                id="delivery"><?php echo $res['delivered'] ?></span></li>

                                    </ul>

                                </div>



                                <div class="oth_feaus">

                                    <ul>

                                        <li class="returns"><a href="return.php" target="_blank"><i
                                                    class="fa fa-exchange"></i><span> Easy Returns and
                                                    Replacement</span></a></li>

                                        <li class="payOptions"><i class="fa fa-credit-card"></i> <span>Payment
                                                Options:</span> (Credit Card , Debit Card , Net Banking , Wallets , COD)
                                        </li>

                                    </ul>

                                </div>

                            </div>

                        </div>

                    </div>

                </div>

            </div>

        </div>

    </section>

    <!-- Section ends -->





    <!-- product-tab starts -->

    <section class=" tab-product  tab-exes">

        <div class="custom-container">

            <div class="row">

                <div class="col-sm-12 col-lg-12 ">

                    <div class=" creative-card creative-inner">

                        <ul class="nav nav-tabs nav-material" id="top-tab" role="tablist">

                            <li class="nav-item"><a class="nav-link active" id="top-home-tab" data-toggle="tab"
                                    href="#top-home" role="tab" aria-selected="true">Description</a>

                                <div class="material-border"></div>

                            </li>

                            <li class="nav-item"><a class="nav-link" id="profile-top-tab" data-toggle="tab"
                                    href="#top-profile" role="tab" aria-selected="false">Details</a>

                                <div class="material-border"></div>

                            </li>



                            <li class="nav-item"><a class="nav-link" id="review-top-tab" data-toggle="tab"
                                    href="#top-review" role="tab" aria-selected="false">Write Review</a>

                                <div class="material-border"></div>

                            </li>

                        </ul>
                        <div class="tab-content nav-material" id="top-tabContent">
                            <div class="tab-pane fade show active" id="top-home" role="tabpanel"
                                aria-labelledby="top-home-tab">
                                <?php echo $res['description']; ?>
                            </div>
                            <div class="tab-pane fade" id="top-profile" role="tabpanel"
                                aria-labelledby="profile-top-tab">
                                <?php echo $res['details']; ?>
                            </div>
                            <div class="tab-pane fade" id="top-review" role="tabpanel" aria-labelledby="review-top-tab">
                                <form class="theme-form">
                                    <div class="form-row">
                                        <div class="col-md-12">
                                            <div class="media">
                                                <label>Rating</label>
                                                <div class="media-body ml-3">
                                                    <div class="rating three-star"><i class="fa fa-star"></i> <i
                                                            class="fa fa-star"></i> <i class="fa fa-star"></i> <i
                                                            class="fa fa-star"></i> <i class="fa fa-star"></i></div>

                                                </div>

                                            </div>

                                        </div>

                                        <div class="col-md-6">

                                            <label for="name">Name</label>

                                            <input type="text" class="form-control" id="name"
                                                placeholder="Enter Your name" required>

                                        </div>





                                        <div class="col-md-12">

                                            <label for="review">Message</label>

                                            <textarea class="form-control" placeholder="Wrire Your Testimonial Here"
                                                id="exampleFormControlTextarea1" rows="6"></textarea>

                                        </div>

                                        <div class="col-md-12">

                                            <button class="btn btn-normal" type="submit">Submit Your Review</button>

                                        </div>

                                    </div>

                                </form>

                                <!--review start-->

                                <section class="review  section-big-py-space">

                                    <div class="fluid-container">

                                        <div class="row review-block">



                                            <div class="col-lg-12">

                                                <h3 class="text-center mb-20">Review</h3>

                                            </div>

                                            <div class="col-lg-3 col-md-6">

                                                <div class="review-box">

                                                    <h5>John Martin</h5>

                                                    <ul class="rating-star">

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                    </ul>

                                                    <div class="review-message">

                                                        <p>Lorem Ipsum is simply dummy text of the printing and
                                                            typesetting industry. Lorem Ipsum has been the industry's
                                                            standard dummy text ever since the 1500s</p>

                                                    </div>

                                                </div>

                                            </div>







                                            <div class="col-lg-3 col-md-6">

                                                <div class="review-box">

                                                    <h5> Himani </h5>



                                                    <ul class="rating-star">

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                    </ul>



                                                    <div class="review-message">

                                                        <p>Lorem Ipsum is simply dummy text of the printing and
                                                            typesetting industry. Lorem Ipsum has been the industry's
                                                            standard dummy text ever since the 1500s</p>

                                                    </div>



                                                </div>

                                            </div>





                                            <div class="col-lg-3 col-md-6">

                                                <div class="review-box">

                                                    <h5> Himani </h5>



                                                    <ul class="rating-star">

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                    </ul>



                                                    <div class="review-message">

                                                        <p>Lorem Ipsum is simply dummy text of the printing and
                                                            typesetting industry. Lorem Ipsum has been the industry's
                                                            standard dummy text ever since the 1500s</p>

                                                    </div>



                                                </div>

                                            </div>



                                            <div class="col-lg-3 col-md-6">

                                                <div class="review-box">

                                                    <h5> Himani </h5>



                                                    <ul class="rating-star">

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                        <li><i class="fa fa-star" aria-hidden="true"></i></li>

                                                    </ul>



                                                    <div class="review-message">

                                                        <p>Lorem Ipsum is simply dummy text of the printing and
                                                            typesetting industry. Lorem Ipsum has been the industry's
                                                            standard dummy text ever since the 1500s</p>

                                                    </div>



                                                </div>

                                            </div>







                                        </div>

                                    </div>

                                </section>

                                <!--review end-->

                            </div>

                        </div>

                    </div>

                </div>

            </div>

        </div>

    </section>

    <!-- product-tab ends -->






    <div class=" product-related mt-4">
        <h2>related products</h2>
    </div>
    <section class="section-big-py-space  ratio_asos bg-light">

        <div class="p-1">




            <div class="product">
                <div class="product-slide-6 ">
                    <?php $trending = dbQuery("SELECT * FROM tabl_products WHERE is_trending='1'  AND status='1'");
                    while ($res_trending = dbFetchAssoc($trending)) {
                        $trending_off = (($res_trending['old_price'] - $res_trending['price']) / $res_trending['old_price']) * 100;
                        $trending_off = round($trending_off, 0);


                        if (isset($_SESSION['user_id'])) {
                            $sel_like = dbQuery("SELECT * FROM tabl_likes WHERE product_id='" . $res_trending['id'] . "' AND user_id = '" . $_SESSION['user_id'] . "'");
                            $num_like = dbNumRows($sel_like);

                            $is_liked = $num_like > 0 ? "fa-solid" : "fa-regular";
                        } else {
                            $is_liked = 'fa-regular';
                        }

                        ?>
                        <div>
                            <div class="product-box">
                                <div class="product-imgbox">
                                    <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>">
                                        <div class="product-front">
                                            <img src="assets/images/products/<?php echo $res_trending['image']; ?>"
                                                class="img-fluid" alt="product">
                                        </div>
                                    </a>

                                    <div class="likeicon"
                                        onclick="likeProduct(event, <?php echo $res_trending['id']; ?>, 2)">
                                        <i class="<?= $is_liked; ?> fa-heart"
                                            id="like_icon_2_<?= $res_trending['id']; ?>"></i>
                                    </div>
                                    <div class="share-btn" onclick="copyLinkToClipboard('<?= $res_trending['id']; ?>')">
                                        <i class="fa-solid fa-share"></i>
                                    </div>

                                    <div class="new-label">
                                        <div><?php echo $trending_off; ?>%</div>
                                    </div>
                                </div>
                                <div class="product-detail detail-center detail-inverse">
                                    <div class="detail-title">

                                        <div class="detail-right">
                                            <div class="check-price">
                                                Rs. <?php echo $res_trending['old_price'] ?>
                                            </div>
                                            <div class="price">
                                                <div class="price">
                                                    Rs. <?php echo $res_trending['price'] ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="detail-left mt-1">

                                            <a href="#">
                                                <h6 class="price-title">
                                                    <?php echo $res_trending['name'] ?>
                                                </h6>
                                            </a>
                                        </div>
                                        <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>"
                                            class="btn btn-rounded mtb-20">Add To Cart </a>
                                        <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>"
                                            class="btn btn-rounded mtb-20">Buy Now </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <!-- <div class="col-12 product"> -->


            <!-- <div class="product-slide no-arrow">
                  <?php $related = dbQuery("SELECT * FROM tabl_products WHERE sub_cat_id='" . $res['sub_cat_id'] . "' AND id!='" . $_REQUEST['productID'] . "'");
                  while ($res_related = dbFetchAssoc($related)) {
                      $off = (($res_related['old_price'] - $res_related['price']) / $res_related['old_price']) * 100;
                      $off = round($off, 0);
                      ?>
                    <div>
                        <div class="product-box">
                            <div class="product-imgbox">
                                 <a href="product-detail.php?productID=<?php echo $res_related['id']; ?>">
                                <div class="product-front">
                                    <img src="assets/images/products/thumb-500/<?php echo $res_related['image']; ?>" class="img-fluid " alt="product">
                                </div>
                                </a>
                                 <div class="new-label">
                                    <div><?php echo $off; ?>%</div>
                                 </div>
                             </div>
                            <div class="product-detail detail-center ">
                                <div class="detail-title">
                                    <div class="detail-left">                                     
                                        <a href="#">
                                            <h6 class="price-title"><?php echo $res_related['name']; ?></h6>
                                        </a>
                                    </div>
                                    <div class="detail-right">
                                        <div class="check-price">Rs. <?php echo $res_related['old_price']; ?></div>
                                        <div class="price">
                                        <div class="price">Rs. <?php echo $res_related['old_price']; ?></div>
                                        </div>
                                    </div>
                                </div>					
                                    <a href="product-detail.php?productID=<?php echo $res_related['id']; ?>" class="btn btn-rounded mtb-20">Add To Cart </a>
                                    <a href="product-detail.php?productID=<?php echo $res_related['id']; ?>" class="btn btn-rounded mtb-20">Buy Now </a>
                            </div>
                        </div>
                    </div>
                           <?php } ?>                
                <div>
              </div>

            </div> -->


        </div>

    </section>

    <!-- related products -->







    <!--footer-start-->



    <?php include ('footer.php') ?>

    <!--footer-end-->







    <!-- tap to top -->

    <div class="tap-top">

        <div>

            <i class="fa fa-angle-double-up"></i>

        </div>

    </div>

    <!-- tap to top End -->





    <!-- latest jquery-->

    <script src="assets/js/jquery-3.3.1.min.js"></script>



    <!-- menu js-->

    <script src="assets/js/menu.js"></script>



    <!-- popper js-->

    <script src="assets/js/popper.min.js"></script>



    <!-- slick js-->

    <script src="assets/js/slick.js"></script>



    <!-- Bootstrap js-->

    <script src="assets/js/bootstrap.js"></script>



    <!-- Theme js-->

    <script src="assets/js/script.js"></script>



</body>

<style>
    .product .product-box:hover .product-detail.detail-center .detail-title {

        opacity: 1;

    }

    .section-big-pt-space {

        padding-top: 0px;

    }



    .mt-50 {

        margin-top: 50px;

    }





    .mr-20 {

        margin-right: 20px;

    }



    .mtb-20 {

        margin-top: 20px;

        margin-bottom: 20px;

    }



    .mtb-50 {

        margin-top: 50px;

        margin-bottom: 50px;

    }





    .btn-rounded {

        font-size: 10px !important;

        padding: 10px 20px;

        font-weight: 700;

        color: #fff !important;

        /* background-color: #cabdb4;*/

        background-color: #832729;

        border-radius: 25px;

        position: relative;

        -webkit-transition: all 0.3s;

        transition: all 0.3s;

        line-height: 1;

        display: inline-block;

        letter-spacing: 0.05em;

    }



    .instagram-box img {

        max-width: 145px;

        height: 145px;

    }





    .box {

        position: relative;

        display: block;

        background: linear-gradient(0deg, black, #444444);

    }



    .glowing::before {

        content: '';

        position: absolute;

        left: -2px;

        top: -2px;

        background: linear-gradient(45deg, #e8f74d, #ff6600d9, #00ff66, #13ff13, #ad27ad, #bd2681, #6512b9, #ff3300de, #5aabde);

        background-size: 400%;

        width: calc(100% + 5px);

        height: calc(100% + 5px);

        z-index: -1;

        animation: glower 20s linear infinite;

    }



    @keyframes glower {

        0% {

            background-position: 0 0;

        }



        50% {

            background-position: 400% 0;

        }



        100% {

            background-position: 0 0;

        }

    }



    .product .product-box {

        margin-right: 15px;

        margin-top: 15px;

        padding-right: 0px;

    }



    .product .product-box .product-imgbox img {

        margin: 0 auto;

        max-width: 100%;

        height: 200px;

    }





    .product .product-box .product-imgbox .new-label {

        position: absolute;

        top: 0;

        left: 0;

        padding: 5px;

        font-weight: 600;

        letter-spacing: 1px;

    }





    .product .product-box .product-imgbox .new-label::before {

        width: 0;

        height: 0;

        border-top: 60px solid #ff708a;

        border-right: 60px solid transparent;

        content: '';

        position: absolute;

        top: 0;

        left: 0;

    }



    .product .product-box .product-imgbox .new-label div {

        color: #fff;

        text-transform: uppercase;

        -webkit-transform: rotate(-45deg);

        transform: rotate(-45deg);

        width: -webkit-fit-content;

        width: -moz-fit-content;

        width: fit-content;

        font-size: calc(10px + (14 - 10) * ((100vw - 320px) / (1920 - 320)));

        margin-top: 3px;

    }



    .location {

        border: #ddd solid 1px;

        color: #757575;

        position: relative;

        padding: 5px 10px;

        width: 200px;

    }



    .status_i {

        clear: both;

        padding-top: 13px;

    }



    .oth_feaus ul li {

        display: block;

        font-size: 14px;

        color: #757575;

        margin-right: 20px;

        margin-bottom: 10px;

        position: relative;

        padding-left: 0px;

    }



    .oth_feaus i {

        color: #2251b5;

    }



    i.fa.fa-check {

        color: #2251b5;

    }



    .status_i ul li {

        display: inline-block;

        font-size: 14px;

        color: #2251b5;

        margin-right: 20px;

        margin-bottom: 10px;

        position: relative;

        padding-left: 0px;

    }



    /*.pin_location ul li:before {

   content: '';

    display: inline-block;

    width: 14px;

    height: 14px;

    text-align: center;

    position: absolute;

    left: 0;

    top: 2px;

    color: #fff;

    font-size: 8px;

    background: #007bff;

    border: 4px solid #ff708a;

    border-radius: 50%;

    line-height: 16px;

}*/





    .pin_location ul li span {

        font-weight: 900;

        color: #ff708a;

    }

    .offers_pdp ul li {

        display: flex;

        align-items: center;

    }



    .coupons_code {

        min-width: 110px;

        margin-right: 16px;

        text-align: center;

    }



    .coupons_code span {

        display: block;

        cursor: pointer;

        font-size: 14px;

        font-weight: 500;

        padding: 3px 8px;

        background: rgba(36, 163, 181, 0.1);

        border: 1px dashed #24a3b5;

        color: #212121;

        -webkit-border-radius: 2px;

        -moz-border-radius: 2px;

        border-radius: 2px;

        -moz-background-clip: padding;

        -webkit-background-clip: padding-box;

        background-clip: padding-box;

    }



    .offers_pdp ul li span {

        font-size: 12px;

        display: block;

        color: #757575;

    }



    .offers_pdp ul li p {

        position: relative;

        font-size: 14px;

        color: #212121;

        font-weight: 400;

        line-height: 1.71;

    }



    .offers_pdp span.h1 {

        position: relative;

        font-size: 18px;

        font-weight: 900;

        line-height: 1.33;

    }



    .offers_pdp span {

        display: inline-block;

    }



    .tab-product .tab-content.nav-material p,
    .product-full-tab .tab-content.nav-material p {

        padding: 5px 0px 0 0px;

        margin-bottom: -8px;

        line-height: 1.5;

        letter-spacing: 0px;

    }





    @media (max-width: 577px) {

        .mobile-fix-option {

            position: fixed;

            bottom: 0;

            left: 0;

            height: 45px;

            /* background-color: #000; */

        }



        body.bg-light {

            /*display: none;*/

        }

    }
</style>

</html>
<script>
    function isNumber(evt) {
        var iKeyCode = (evt.which) ? evt.which : evt.keyCode
        if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
            return false;

        return true;
    }

    function isDecimal(evt, obj) {

        var charCode = (evt.which) ? evt.which : event.keyCode
        var value = obj.value;
        var dotcontains = value.indexOf(".") != -1;
        if (dotcontains)
            if (charCode == 46) return false;
        if (charCode == 46) return true;
        if (charCode > 31 && (charCode < 48 || charCode > 57))
            return false;
        return true;
    }
</script>
<script>
    $("#check_pincode").submit(function (e) {
        $("#submit").attr('disabled', true);
        $(".pincode_loader").html('<img src="loader.gif" syle="width: 20px;"></i> please wait...');
        $.ajax({
            url: 'ajax/check_pincode.php',
            type: 'post',
            data: $("#check_pincode").serialize(),
            success: function (data) {
                if (data == 1) {
                    $("#submit").attr('disabled', false);
                    $(".pincode_loader").html('<span style="color: #59a759;font-weight: 500;"><i class="fa fa-check" style="color: #59a759;"></i> Delivery Available!</span>');
                }
                else {
                    $("#submit").attr('disabled', false);
                    $(".pincode_loader").html('<span style="color: #e44d14;font-weight: 500;"><i class="fa fa-trash-o" style="color: #e44d14;"></i> No Delivery Available!</span>');
                }
            },
        });
        e.preventDefault(); // avoid to execute the actual submit of the form.
    });
</script>
<script>
    $(".bg-light0").click(function () {
        $(".bg-light0").removeClass("select-color");
        $(this).addClass('select-color');
        var color_id = $(this).attr("data-id");
        $("#color_option").val(color_id);
        $(".color_msg").html('');

    });
    $(".size-option").click(function () {
        $(".size-option").removeClass("select-size");
        $(this).addClass('select-size');
        var size_option = $(this).attr("data-id");
        var new_size_option = size_option.split('~~~');
        $("#size_option").val(new_size_option[0]);
        $("#price").val(new_size_option[1]);
        $(".size_msg").html('');
        $(".price-text").html('Rs ' + new_size_option[1]);
        var old_price = <?php echo $res['old_price']; ?>;
        var off = ((parseFloat(old_price) - parseFloat(new_size_option[1])) / parseFloat(new_size_option[1])) * 100;
        $(".off-text").html(Math.round(off) + '% off');
    });

    function add_to_wishlist() {
        $.ajax({
            url: 'ajax/add_to_wishlist.php',
            method: 'POST',
            data: { id: '<?= $_GET['productID'] ?>' },
            success: (res) => {
                alert('Product added to wishlist');
            }
        })
    }

</script>


<style>
    .select-color {
        border: 2px solid #151212 !important;
    }

    .select-size {
        border: 2px solid #ca2222 !important;
    }
</style>



<script>
    function likeProduct(event, product_id, type) {
        event.stopPropagation();

        const icon = document.getElementById("like_icon_" + type + "_" + product_id);
        const like_text = document.getElementById("like_text");

        // alert("Like ID: like_" + propertyId);

        // Make an AJAX call to the like_dislike.php script
        const xhr = new XMLHttpRequest();
        xhr.open("POST", "./ajax/like_dislike.php", true);
        xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4 && xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                    // Toggle class and change title based on response
                    // btn.classList.toggle("liked", response.isLiked);


                    if (response.isLiked) {
                        // if (icon.classList.contains('fa-regular')) {
                        icon.classList.remove('fa-regular');
                        icon.classList.add('fa-solid');
                    } else {
                        icon.classList.remove('fa-solid');
                        icon.classList.add('fa-regular');
                        // }
                    }

                    // like_text.innerHTML = response.isLiked ? "Liked" : "Like";
                } else {
                    console.error("An error occurred: " + response.message);
                    alert(response.message);
                }
            }
        };
        xhr.send("product_id=" + product_id);
    }



    function copyLinkToClipboard(pro_id) {
        // Create a temporary textarea element to hold the link
        const textarea = document.createElement('textarea');
        textarea.value = 'https://devaking777.com/product-detail.php?productID=' + pro_id;
        document.body.appendChild(textarea);

        // Select the text in the textarea
        textarea.select();
        textarea.setSelectionRange(0, 99999); // For mobile devices

        // Copy the selected text to the clipboard
        try {
            const successful = document.execCommand('copy');
            const message = successful ? 'Link copied to clipboard!' : 'Failed to copy link.';
            console.log(message);
            alert(message);
        } catch (err) {
            console.error('Oops, unable to copy:', err);
        }

        // Remove the temporary textarea element
        document.body.removeChild(textarea);
    }
</script>