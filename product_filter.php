<div class="col-sm-3 collection-filter category-page-side mt-30">
			  <!-- side-bar colleps block stat -->
                    <div class="collection-filter-block creative-card creative-inner category-side">
                        <!-- brand filter start -->

                        <div class="collection-mobile-back"><span class="filter-back"><i class="fa fa-angle-left" aria-hidden="true"></i> back</span></div>

                        <div class="collection-collapse-block open">

                            <h3 class="collapse-block-title mt-0">Sort</h3>

                            <div class="collection-collapse-block-content">

                                <div class="collection-brand-filter">
								             <div style="margin-top: 17px;">						 
							  <input type="radio" id="f_sort1" class="f_sort" name="f_sort" value="1" <?php if(@$_REQUEST['f_sort']==1){echo 'checked';}else{echo'';} ?>>
							  <label for="f_sort1" class="radio_f_sort">Low to High</label><br>
							  <input type="radio" id="f_sort2" class="f_sort" name="f_sort" value="2" <?php if(@$_REQUEST['f_sort']==2){echo 'checked';}else{echo'';} ?>>
							  <label for="f_sort2" class="radio_f_sort">HIGH TO LOW</label><br>  
							  <input type="radio" id="f_sort3" class="f_sort" name="f_sort" value="3" <?php if(@$_REQUEST['f_sort']==3){echo 'checked';}else{echo'';} ?>>
							  <label for="f_sort3" class="radio_f_sort">POPULARITY</label><br>
							  <input type="radio" id="f_sort4" class="f_sort" name="f_sort" value="4" <?php if(@$_REQUEST['f_sort']==4){echo 'checked';}else{echo'';} ?>>
							  <label for="f_sort4" class="radio_f_sort">BEST PRODUCT</label><br>
											</div>
                                </div>

                            </div>

                        </div>

                        <!-- color filter start here -->

                        <div class="collection-collapse-block open">

                            <h3 class="collapse-block-title">colors</h3>

                            <div class="collection-collapse-block-content">							     
								 <div class="collection-brand-filter">
                                   <?php 
								  
								   $s_color=dbQuery("SELECT `tabl_product_options`.*, tabl_product_variation_value.id AS option_id,tabl_product_variation_value.v_value,color,tabl_products.sub_cat_id FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_options.v_id=tabl_product_variation_value.id INNER JOIN tabl_products ON tabl_product_options.p_id=tabl_products.id WHERE tabl_products.sub_cat_id='".$_REQUEST['cateoryID']."' AND tabl_product_options.v_type=1 GROUP BY tabl_product_variation_value.v_value ORDER BY tabl_product_variation_value.id");
								   while($res_s_color=dbFetchAssoc($s_color)){
									   if(@$_REQUEST['color']!=""){
                                          if (in_array($res_s_color['option_id'], @$_REQUEST['color']))
													  {
													 $checked='checked';
													  }
													else
													  {
													 $checked='';
													  }
									   }else{										   
										   $checked='';
									   }	  
									   
								   ?>                                   
								   <div class="custom-control custom-checkbox collection-filter-checkbox">
                                        <input type="checkbox" class="custom-control-input sort_color" id="color_<?php echo $res_s_color['option_id']?>" name="color" value="<?php echo $res_s_color['option_id'] ?>" <?php echo $checked;?>>
                                        <label class="custom-control-label" for="color_<?php echo $res_s_color['option_id']?>"><div style="background-color:<?php echo $res_s_color['color'];?>;width: 17px;height: 17px;"></div></label>

                                    </div>
								   <?php }?>	
                                </div>
                            </div>
                        </div>

						

						

                        <!-- price filter start here -->

                        <div class="collection-collapse-block border-0 open">

                            <h3 class="collapse-block-title">Size</h3>

                            <div class="collection-collapse-block-content">

                                <div class="collection-brand-filter">
                                     <?php $s_size=dbQuery("SELECT `tabl_product_options`.*, tabl_product_variation_value.id AS option_id,tabl_product_variation_value.v_value,color,tabl_products.sub_cat_id FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_options.v_id=tabl_product_variation_value.id INNER JOIN tabl_products ON tabl_product_options.p_id=tabl_products.id WHERE tabl_products.sub_cat_id='".$_REQUEST['cateoryID']."' AND tabl_product_options.v_type=2 GROUP BY tabl_product_variation_value.v_value ORDER BY tabl_product_variation_value.id");
								   while($res_s_size=dbFetchAssoc($s_size)){
                                       if(@$_REQUEST['size']!=""){
                                          if (in_array($res_s_size['option_id'], @$_REQUEST['size']))
													  {
													 $checked1='checked';
													  }
													else
													  {
													 $checked1='';
													  }
									   }else{										   
										   $checked1='';
									   }	 									   
								   ?>            
									<div class="custom-control custom-checkbox collection-filter-checkbox">
										
										 <input type="checkbox" class="custom-control-input sort_size" id="size_<?php echo $res_s_size['option_id']?>" name="size" value="<?php echo $res_s_size['option_id'] ?>" <?php echo $checked1;?>>
										
                                        <label class="custom-control-label" for="size_<?php echo $res_s_size['option_id']?>"><?php echo $res_s_size['v_value'];?></label>
                                    </div>
								   <?php }?>

                                </div>

                            </div>

                        </div>

						

						 <div class="collection-collapse-block border-0 open">

                            <h3 class="collapse-block-title">Availability</h3>

                            <div class="collection-collapse-block-content">

                                <div class="collection-brand-filter">
                                 <?php $avil=dbQuery("SELECT tabl_stock.*,tabl_products.* FROM tabl_products INNER JOIN tabl_stock ON tabl_stock.p_id=tabl_products.id WHERE tabl_products.sub_cat_id='".$_REQUEST['cateoryID']."' AND (tabl_stock.in_stock-tabl_stock.out_stock=0)");
                                     $av_num=dbNumRows($avil);
									 
									  if(@$_REQUEST['availablity']!=""){
                   						   $checked2='checked';
									   }else{										   
										    $checked2='';
									   }
									 
									 ?> 
 
                                    <div class="custom-control custom-checkbox collection-filter-checkbox">
       										 <input type="checkbox" class="custom-control-input sort_av" id="availablity" name="availablity" value="0" <?php echo $checked2; ?>>									
										
                                        <label class="custom-control-label" for="availablity">
                                        Include out of Stock (<?php echo $av_num;?>)</label>
                                    </div>
									
									
                                </div>

                            </div>

                        </div>

						

						

						 <div class="collection-collapse-block border-0 open d-none">

                            <h3 class="collapse-block-title">Price</h3>

                            <div class="collection-collapse-block-content">

                                <div class="collection-brand-filter">

                                    <div class="custom-control custom-checkbox collection-filter-checkbox">
									 <?php $price1=dbQuery("SELECT * FROM tabl_products WHERE price>=499 AND sub_cat_id='". $_REQUEST['cateoryID']."'");
									 $num_price1=dbNumRows($price1); ?>
                                        <input type="checkbox" class="custom-control-input sort_price" id="price1" name="price" value="1" <?php if(@$_REQUEST['price']!=""){
											if(in_array(1, @$_REQUEST['price'])){
										echo 'checked';}else{echo '';}
										}?>>
										
                                        <label class="custom-control-label" for="price1">Rs. 499 and Below (<?php echo $num_price1;?>)</label>
                                    </div>							

									<div class="custom-control custom-checkbox collection-filter-checkbox">
                                        <?php $price2=dbQuery("SELECT * FROM tabl_products WHERE price>=500 AND price<=999 AND sub_cat_id='". $_REQUEST['cateoryID']."'");
									       $num_price2=dbNumRows($price2);?>
                                        <input type="checkbox" class="custom-control-input sort_price" id="price2" name="price" value="2" <?php if(@$_REQUEST['price']!=""){
											if(in_array(2, @$_REQUEST['price'])){
										echo 'checked';}else{echo '';}
										}?>>
                                          <label class="custom-control-label" for="price2">
                                        Rs. 500 - Rs. 999 (<?php echo  $num_price2; ?>)</label>
                                    </div>				

									<div class="custom-control custom-checkbox collection-filter-checkbox">
                                       <?php $price3=dbQuery("SELECT * FROM tabl_products WHERE price>=1000 AND price<=1999 AND sub_cat_id='". $_REQUEST['cateoryID']."'");
									       $num_price3=dbNumRows($price3);?>
                                        <input type="checkbox" class="custom-control-input sort_price" id="price3" name="price" value="3" <?php if(@$_REQUEST['price']!=""){
											if(in_array(3, @$_REQUEST['price'])){
										echo 'checked';}else{echo '';}
										}?>>
                                          <label class="custom-control-label" for="price3">
                                        <label class="custom-control-label" for="price3">Rs. 1000 - Rs. 1999 (<?php echo $num_price3;?>)</label>
                                    </div>		

									<div class="custom-control custom-checkbox collection-filter-checkbox">
                                          <?php $price4=dbQuery("SELECT * FROM tabl_products WHERE price>=2000 AND sub_cat_id='". $_REQUEST['cateoryID']."'");
									       $num_price4=dbNumRows($price4);?>
                                        <input type="checkbox" class="custom-control-input sort_price" id="price4" name="price" value="4" <?php if(@$_REQUEST['price']!=""){
											if(in_array(4, @$_REQUEST['price'])){
										echo 'checked';}else{echo '';}
										}?>>
                                          <label class="custom-control-label" for="price4">
                                        <label class="custom-control-label" for="price4">Rs. 2000 and Above (<?php echo $num_price4;?>)</label>
                                    </div>

                                </div>

                            </div>

                        </div>

                    </div>
                    <!-- silde-bar colleps block end here -->
               </div>