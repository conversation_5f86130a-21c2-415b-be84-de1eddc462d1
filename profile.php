<?php 
session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
include('lib/auth.php');
include('admin/inc/resize-class.php');
$page=0;
$sub_page=0;
$profile=2;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d');
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>

  <title>Deva King 777</title>

  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <meta name="viewport" content="width=device-width,initial-scale=1">

  <link rel="icon" href="./assets/img/logo/icon.png">

	

	

  <!--Google font-->

  <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">

  <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">

  

  

	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

 



  <!--icon css-->

  <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">

  <link rel="stylesheet" type="text/css" href="assets/css/themify.css">



  <!--Slick slider css-->

  <link rel="stylesheet" type="text/css" href="assets/css/slick.css">

  <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">



  <!--Animate css-->

  <link rel="stylesheet" type="text/css" href="assets/css/animate.css">

  <!-- Bootstrap css -->

  <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">



  <!-- Theme css -->

  <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
  <!-- custom responsive css -->
  <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">

</head>

<body class="bg-light ">



<!-- loader start -->

<div class="loader-wrapper">
        <div>
            <!-- <img src="assets/images/loader.gif" alt="loader"> -->
            <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
        </div>
    </div>

<!-- loader end -->



<!--header start-->



   <?php include('header.php')?>

<!--header end-->





<!-- breadcrumb start -->

<div class="breadcrumb-main ">

    <div class="container">

        <div class="row">

            <div class="col">

                <div class="breadcrumb-contain">

                    <div>

                        <h2>My-Account</h2>

                        <ul>

                            <li><a href="account.php">home</a></li>

                            <li><i class="fa fa-angle-double-right"></i></li>

                            <li><a href="profile.php">Profile</a></li>

                        </ul>

                    </div>

                </div>

            </div>

        </div>

    </div>

</div>

<!-- breadcrumb End -->



<!-- section start -->

<section class="section-big-py-space bg-light">

    <div class="container">

        <div class="row">

             <?php include('account_sidebar.php');?>

            <div class="col-lg-9">	

               <div class="dashboard-right">

                    <div class="dashboard" style="padding-top: 1px;">
                     <div class="box-account box-info">
                        <div class="box-head">
                         <h2>Profile Information</h2></div>
                          <?php $sel=dbQuery("SELECT * FROM tabl_customer WHERE id='".$_SESSION['customer_id']."'");
                                 $res=dbFetchAssoc($sel);?>
                            <div class="row mt-20">

                               <div class="col-sm-12">

								 <form class="theme-form" id="profile_form" method="post">

									<div class="form-row">

										<div class="col-md-12">

										   <div class="form-group">

											   <label for="name">Name</label>

											   <input type="text" class="form-control" id="name" name="name" value="<?php echo $res['name']; ?>" placeholder="Enter Your Name" required="">

										   </div>

										</div>

										<div class="col-md-6">

										   <div class="form-group">

											   <label for="review">Phone number</label>

											   <input type="text" class="form-control" id="review" placeholder="Enter your number" name="phone" value="<?php echo $res['phone']; ?>" required="" disabled>

										   </div>

										</div>

										<div class="col-md-6">

											<div class="form-group">

												<div class="form-group">

													<label for="email">Email</label>

													<input type="email" class="form-control" id="email" placeholder="Email" required="" name="email" value="<?php echo $res['email']; ?>" disabled>

												</div>

											</div>

										</div>

										
										<div class="col-md-6">

											<div class="form-group">

												<div class="form-group">

													<label for="address">Address</label>

													<input type="address" class="form-control" id="adress" placeholder="Address" required="" name="email" value="<?php echo $res['address']; ?>" disabled>

												</div>

											</div>

										</div>

										<div class="col-md-7">

										<button type="submit" id="submit" class="btn btn-normal pull-right">Update</button>

										</div>

									</div>
<div class="loader_login"></div>
								</form>

                                </div>

                                

                            </div>

                           

                        </div>

                    </div>

                </div>

            </div>

        </div>

    </div>

</section>

<!-- section end -->







<!--footer-start-->



   <?php include('footer.php')?>

<!--footer-end-->





<!-- tap to top -->

<div class="tap-top">

  <div>

    <i class="fa fa-angle-double-up"></i>

  </div>

</div>

<!-- tap to top End -->







<!-- latest jquery-->

<script src="assets/js/jquery-3.3.1.min.js"></script>



<!-- slick js-->

<script src="assets/js/slick.js"></script>



<!-- popper js-->

<script src="assets/js/popper.min.js" ></script>



<!-- Timer js-->

<script src="assets/js/menu.js"></script>



<!-- Bootstrap js-->

<script src="assets/js/bootstrap.js"></script>



<!-- Bootstrap js-->

<script src="assets/js/bootstrap-notify.min.js"></script>



<!-- Theme js-->

<script src="assets/js/script.js"></script>

</body>
</html>
<script>
 $("#profile_form").submit(function(e) {
	$("#submit").attr('disabled',true);
	$(".loader_login").html('<img src="loader.gif"></i> please wait...');	
 $.ajax({
	  url:'ajax/customer_profile_update.php',
	  type:'post',
	  data:$("#profile_form").serialize(),
	  success:function(data){
	if(data==1)
		{     
	$("#submit").attr('disabled',false);
	  $(".loader_login").html('<div class="alert alert-success" role="alert">Profile Update successfully!</div>');	
	setTimeout(function() {
      $(".loader_login").html('');	 
					}, 8000);  
		}
	else{
	  $("#submit").attr('disabled',false);
	  $(".loader_login").html('<div class="alert alert-danger" role="alert">Either username or password are wrong!</div>');	
	setTimeout(function() {
      $(".loader_login").html('');	 
					}, 8000);	

			}
		},
	  });
   e.preventDefault(); // avoid to execute the actual submit of the form.
});
</script>


<style>

.mt-20 {

    margin-top: 20px;

}



.btn-normal {

    font-size: 14px;

    padding: 15px 25px;

}

</style>

