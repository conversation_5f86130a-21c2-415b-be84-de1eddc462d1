name: SecurityChecks
on:
  pull_request: {}
  push:
    branches: ["master"]
  schedule:
    - cron: '30 20 * * *'  
jobs:
  semgrep:
    name: Semgrep
    runs-on: [ubuntu-latest]
    container:
      image: returntocorp/semgrep
    steps:
      - uses: actions/checkout@v4.0.0
      - name: Run semgrep
        run: semgrep ci
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}

  workflow_status:
    runs-on: [ ubuntu-latest ]                               # nosemgrep : semgrep.dev/s/swati31196:github_provided_runner
    name: Update Status Check
    needs: [ semgrep ]
    if: always()
    env:
      githubCommit: ${{ github.event.pull_request.head.sha }}
    steps:
      - name: Set github commit id
        run: |
          if [ "${{ github.event_name }}" = "push" ] || [ "${{ github.event_name }}" = "schedule" ]; then
            echo "githubCommit=${{ github.sha }}" >> $GITHUB_ENV
          fi
          exit 0
      - name: Failed
        id: failed
        if: (contains(needs.*.result, 'failure') || contains(needs.*.result, 'cancelled')) && github.ref != 'refs/heads/master'
        run: |
          echo 'Failing the workflow for github security status check.'
          curl -X POST -H "Content-Type: application/json" -H "Authorization: token ${{ github.token }}" \
          -d '{ "state" : "failure" , "context" : "github/security-status-check" , "description" : "github/security-status-check", "target_url" : "https://github.com/${{ github.repository }}" }' \
          https://api.github.com/repos/${{ github.repository }}/statuses/${{ env.githubCommit }}
          exit 1
      - name: Success
        if: steps.failed.conclusion == 'skipped' || github.ref != 'refs/heads/master'
        run: |
          echo 'Status check has passed!'
          curl -X POST -H "Content-Type: application/json" -H "Authorization: token ${{ github.token }}" \
          -d '{ "state" : "success" , "context" : "github/security-status-check" , "description" : "github/security-status-check", "target_url" : "https://github.com/${{ github.repository }}" }' \
          https://api.github.com/repos/${{ github.repository }}/statuses/${{ env.githubCommit }}
          exit 0
