## Linked Account

### Create Linked Accounts for Sub-Merchants and Others
```php


$api->setHeader('X-Razorpay-Account', 'acc_sub-merchantId');

$api->account->create(array(
    "email" => "<EMAIL>",
    "phone" => "**********",
    "type" => "route",
    "reference_id" => "124124",
    "legal_business_name" => "Acme Corp",
    "business_type" => "partnership",
    "contact_name" => "Gaura<PERSON> Kumar",
    "profile" => array(
        "category" => "healthcare",
        "subcategory" => "clinic",
        "addresses" => array(
            "registered" => array(
                "street1" => "507, Koramangala 1st block",
                "street2" => "MG Road",
                "city" => "Bengaluru",
                "state" => "Karnataka",
                "postal_code" => 560034,
                "country" => "IN"
            )
        ),
    ),
    "legal_info" => array(
        "pan" => "**********",
        "gst" => "18AABCU9603R1ZM"
    ),
));

```

**Parameters:**

| Name          | Type        | Description                                 |
|---------------|-------------|---------------------------------------------|
| email*        | string      | The sub-merchant's business email address.  |
| phone*          | integer      | The sub-merchant's business phone number. The minimum length is 8 characters and the maximum length is 15.                       |
| legal_business_name*      | string | The name of the sub-merchant's business. For example, Acme Corp. The minimum length is 4 characters and the maximum length is 200.          |
| customer_facing_business_name | string | The sub-merchant billing label as it appears on the Razorpay Dashboard. The minimum length is 1 character and the maximum length is 255. |
| business_type*         | string      | The type of business operated by the sub-merchant.Possible value is `proprietorship`, `partnership`, `private_limited`, `public_limited`, `llp`, `ngo`, `trust`, `society`, `not_yet_registered`, `huf` |
| reference_id         | string      |  Partner's external account reference id. The minimum length is 1 character and the maximum length is 512. |
| profile         | object      | All keys listed [here](https://razorpay.com/docs/partners/route/linked-accounts/#create-linked-accounts-for-sub-merchants-and-others) are supported |         
| legal_info         | object      | All keys listed [here](https://razorpay.com/docs/partners/route/linked-accounts/#create-linked-accounts-for-sub-merchants-and-others) are supported |
| contact_info | object  | All keys listed [here](https://razorpay.com/docs/api/partners/account-onboarding/#create-an-account) are supported |     
| apps | object  | All keys listed [here](https://razorpay.com/docs/api/partners/account-onboarding/#create-an-account) are supported |     


**Response:**
```json
{
   "id":"acc_GRWKk7qQsLnDjX",
   "type":"route",
   "status":"created",
   "email":"<EMAIL>",
   "profile":{
      "category":"healthcare",
      "subcategory":"clinic",
      "addresses":{
         "registered":{
            "street1":"507, Koramangala 1st block",
            "street2":"MG Road",
            "city":"Bengaluru",
            "state":"KARNATAKA",
            "postal_code":"560034",
            "country":"IN"
         }
      }
   },
   "notes":[
      
   ],
   "created_at":**********,
   "phone":"**********",
   "contact_name":"Gaurav Kumar",
   "reference_id":"124124",
   "business_type":"partnership",
   "legal_business_name":"Acme Corp",
   "customer_facing_business_name":"Acme Corp",
   "legal_info":{
      "pan":"**********",
      "gst":"18AABCU9603R1ZM"
   }
}
```

-------------------------------------------------------------------------------------------------------

### Fetch a Linked Account of a Sub-Merchant by id
```php
$api->setHeader('X-Razorpay-Account', 'acc_sub-merchantId');

$accountId = "acc_GP4lfNA0iIMn5B";
$api->account->fetch($accountId);
```

**Parameters:**

| Name        | Type        | Description                                 |
|-------------|-------------|---------------------------------------------|
| accountId* | string      | The unique identifier of a sub-merchant account generated by Razorpay.  |

**Response:**
```json
{
   "id":"acc_GLGeLkU2JUeyDZ",
   "type":"route",
   "reference_id":"123123",
   "status":"created",
   "email":"<EMAIL>",
   "profile":{
      "category":"healthcare",
      "subcategory":"clinic",
      "addresses":{
         "registered":{
            "street1":"507, Koramangala 1st block",
            "street2":"MG Road",
            "city":"Bengaluru",
            "state":"KARNATAKA",
            "postal_code":560034,
            "country":"IN"
         },
         "operation":{
            "street1":"507, Koramangala 6th block",
            "street2":"Kormanagala",
            "city":"Bengaluru",
            "state":"KARNATAKA",
            "country":"IN",
            "postal_code":560047
         }
      },
      "business_model":null
   },
   "notes":[
      
   ],
   "created_at":**********,
   "phone":"**********",
   "business_type":"partnership",
   "legal_business_name":"Acme Corp",
   "customer_facing_business_name":"Acme Corp",
   "legal_info":{
      "pan":"**********",
      "gst":"18AABCU9603R1ZM"
   },
   "apps":{
      "websites":[
         
      ],
      "android":[
         {
            "url":null,
            "name":null
         }
      ],
      "ios":[
         {
            "url":null,
            "name":null
         }
      ]
   },
   "brand":{
      "color":null
   },
   "contact_name":"Gaurav Kumar",
   "contact_info":{
      "chargeback":{
         "email":null,
         "phone":null,
         "policy_url":null
      },
      "refund":{
         "email":null,
         "phone":null,
         "policy_url":null
      },
      "support":{
         "email":null,
         "phone":null,
         "policy_url":null
      }
   }
}
```

-------------------------------------------------------------------------------------------------------

**PN: * indicates mandatory fields**
<br>
<br>
**For reference click [here](https://razorpay.com/docs/partners/route/linked-accounts)**