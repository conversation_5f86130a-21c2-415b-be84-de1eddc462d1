<?php
session_start();
include('../admin/lib/db_connection.php');
include('../lib/auth_2.php');
require('./config.php');
require('./Razorpay.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');
$session_id = session_id();


error_reporting(E_ERROR | E_WARNING | E_PARSE | E_NOTICE);
// Create the Razorpay Order
use Razorpay\Api\Api;

$api = new Api($keyId, $keySecret);


// $sel_oder = dbQuery("SELECT * FROM tabl_my_plans ORDER BY id DESC LIMIT 1");
// $res_oder = dbFetchAssoc($sel_oder);


$_SESSION['plan_id'] = $_REQUEST['plan_id'];
$plan_id = $_REQUEST['plan_id'];

$sel_plan = dbQuery("SELECT * FROM tabl_plans WHERE `id`='" . $plan_id . "' ");
$res_plan = dbFetchAssoc($sel_plan);


// response_id='" . $response_id . "',
// tx_no='" . $tx_no . "',


$status = dbQuery("INSERT INTO tabl_my_plans SET 
plan_id='" . $plan_id . "',
user_id='" . $user['id'] . "',
price='" . $res_plan['price'] . "', 
fname='" . $_REQUEST['fname'] . "', 
lname='" . $_REQUEST['lname'] . "', 
company='" . $_REQUEST['company'] . "', 
email='" . $_REQUEST['email'] . "', 
phone='" . $_REQUEST['phone'] . "', 
location='" . $_REQUEST['location_id'] . "', 
address='" . $_REQUEST['address'] . "',
status='0',
date='" . $date . "'
");

$order_id = dbInsertId();


// $order_id = $res_oder['id'] + 1;

$_SESSION['receipt_id'] = $order_id;
$_SESSION['order_total_amount'] = $res_plan['price'];

// $_SESSION['order_payment_type'] = $_REQUEST['payment'];
$_SESSION['order_payment_type'] = "Online";

$new_price = $res_plan['price'];
$product_name = 'New Order# ' . $order_id . '';



$name = $_REQUEST['fname'] . ' ' . $_REQUEST['lname']; // name
$email = $_REQUEST['email']; // email
$phone = $_REQUEST['phone']; // phone



// We create an razorpay order using orders api
// Docs: https://docs.razorpay.com/docs/orders
//
$orderData = [
    'receipt' => '#' . $_SESSION['receipt_id'],
    'amount' => $new_price * 100, // 2000 rupees in paise
    'currency' => 'INR',
    'payment_capture' => 1 // auto capture
];

$razorpayOrder = $api->order->create($orderData);
$razorpayOrderId = $razorpayOrder['id'];

$_SESSION['razorpay_order_id'] = $razorpayOrderId;

dbQuery("UPDATE tabl_my_plans SET response_id='" . $_SESSION['razorpay_order_id'] . "' WHERE id='" . $order_id . "'");

$displayAmount = $amount = $orderData['amount'];

if ($displayCurrency !== 'INR') {
    $url = "https://api.fixer.io/latest?symbols=$displayCurrency&base=INR";
    $exchange = json_decode(file_get_contents($url), true);
    $displayAmount = $exchange['rates'][$displayCurrency] * $amount / 100;
}

$checkout = 'automatic';
if (isset($_GET['checkout']) and in_array($_GET['checkout'], ['automatic', 'manual'], true)) {
    $checkout = $_GET['checkout'];
}

$data = [
    "key" => $keyId,
    "amount" => $amount,
    "name" => '5000 Property',
    // "description" => "Purchase Serice",
    "description" => $product_name,
    "image" => "https://5000property.com/images/logo/5000_logo.png",
    "prefill" => [
        "name" => $name,
        "email" => $email,
        "contact" => $phone,
    ],
    "notes" => [
        "address" => '',
    ],
    "theme" => [
        "color" => "#ff6600"
    ],
    "order_id" => $razorpayOrderId,
];

if ($displayCurrency !== 'INR') {
    $data['display_currency'] = $displayCurrency;
    $data['display_amount'] = $displayAmount;
}

$json = json_encode($data);
require("../checkout/{$checkout}.php");
?>

<a href="https://5000property.com/">Back to Merchant Website 5000 Property</a>