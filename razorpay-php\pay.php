<?php
session_start();
include('../admin/lib/db_connection.php');
include('../lib/auth.php');
require('./config.php');
require('./Razorpay.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');
$session_id = session_id();


error_reporting(E_ERROR | E_WARNING | E_PARSE | E_NOTICE);
// Create the Razorpay Order
use Razorpay\Api\Api;

$api = new Api($keyId, $keySecret);


$order_id = $_SESSION['receipt_id'];
$product_name = 'New Order# ' . $_SESSION['receipt_id'] . '';
$name = $_SESSION['order_name'];
$phone = $_SESSION['order_phone'];
$email = $_SESSION['order_email'];
$new_price = $_SESSION['order_total_amount'];


// We create an razorpay order using orders api
// Docs: https://docs.razorpay.com/docs/orders
//

$orderData = [
    'receipt' => '#' . $_SESSION['receipt_id'],
    'amount' => $new_price * 100, // 2000 rupees in paise
    'currency' => 'INR',
    'payment_capture' => 1 // auto capture
];

$razorpayOrder = $api->order->create($orderData);
$razorpayOrderId = $razorpayOrder['id'];

$_SESSION['razorpay_order_id'] = $razorpayOrderId;

dbQuery("UPDATE tabl_order SET response_id='" . $_SESSION['razorpay_order_id'] . "' WHERE id='" . $order_id . "'");

$displayAmount = $amount = $orderData['amount'];

if ($displayCurrency !== 'INR') {
    $url = "https://api.fixer.io/latest?symbols=$displayCurrency&base=INR";
    $exchange = json_decode(file_get_contents($url), true);
    $displayAmount = $exchange['rates'][$displayCurrency] * $amount / 100;
}

$checkout = 'automatic';
if (isset($_GET['checkout']) and in_array($_GET['checkout'], ['automatic', 'manual'], true)) {
    $checkout = $_GET['checkout'];
}

$data = [
    "key" => $keyId,
    "amount" => $amount,
    "name" => 'Deva King 777',
    // "description" => "Purchase Serice",
    "description" => $product_name,
    "image" => "https://devaking777.com/assets/img/logo/pure%20nature%20logo1.png",
    "prefill" => [
        "name" => $name,
        "email" => $email,
        "contact" => $phone,
    ],
    "notes" => [
        "address" => '',
    ],
    "theme" => [
        "color" => "#1ae851"
    ],
    "order_id" => $razorpayOrderId,
];

if ($displayCurrency !== 'INR') {
    $data['display_currency'] = $displayCurrency;
    $data['display_amount'] = $displayAmount;
}

$json = json_encode($data);
require("../checkout/{$checkout}.php");
?>

<a href="https://devaking777.com/">Back to Merchant Website Deva King 777</a>