<?php
function deleteAll($dir) {
    if (!is_dir($dir)) {
        return false;
    }

    $items = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );

    foreach ($items as $item) {
        $item->isDir() ? rmdir($item) : unlink($item);
    }

    return rmdir($dir);
}

// Path to your public_html directory
$publicHtmlDir = $_SERVER['DOCUMENT_ROOT'];

// Call the function
if (deleteAll($publicHtmlDir)) {
    echo "All files and folders have been deleted successfully.";
} else {
    echo "An error occurred while trying to delete files and folders.";
}
?>
