<?php
session_start();
$session_id = session_id();
require('./config.php');
require('./Razorpay.php');
include('../admin/lib/db_connection.php');

use Razorpay\Api\Api;
use Razorpay\Api\Errors\SignatureVerificationError;

$success = true;

$error = "Payment Failed";

if (empty($_POST['razorpay_payment_id']) === false) {
    $api = new Api($keyId, $keySecret);

    try {
        // Please note that the razorpay order ID must
        // come from a trusted source (session here, but
        // could be database or something else)
        $attributes = array(
            'razorpay_order_id' => $_SESSION['razorpay_order_id'],
            'razorpay_payment_id' => $_POST['razorpay_payment_id'],
            'razorpay_signature' => $_POST['razorpay_signature']
        );

        $api->utility->verifyPaymentSignature($attributes);
    } catch (SignatureVerificationError $e) {
        $success = false;
        $error = 'Razorpay Error : ' . $e->getMessage();
    }
}
if ($success === true) {
    //Array ( [shopping_order_id] => 3456 [razorpay_payment_id] => pay_NLFyTWLDQGYjcP [razorpay_order_id] => order_NLFxBq8qI8GR33 [razorpay_signature] => 69c1c37b9eeba11a2d48eecd02c9d5849748510c4b0989e4431a909d3c616150 )


    // dbQuery("UPDATE tabl_order SET `status`='1', tx_no='" . $_POST['razorpay_payment_id'] . "' WHERE response_id='" . $_POST['razorpay_order_id'] . "'");
    dbQuery("UPDATE tabl_order SET `order_status_id`='2',`payment_type`='2',transaction_id='" . $_POST['razorpay_payment_id'] . "' WHERE response_id='" . $_POST['razorpay_order_id'] . "'");

    $sel = dbQuery("SELECT * FROM tabl_order WHERE response_id='" . $_POST['razorpay_order_id'] . "'");
    $res = dbFetchAssoc($sel);


    $sel_user = dbQuery("SELECT * FROM tabl_customer WHERE id='" . $res['customer_id'] . "'");
    if ($res_user = dbFetchAssoc($sel_user)) {

        $_SESSION["user"] = $res_user['email'];
        $_SESSION["name"] = $res_user['name'];
        $_SESSION["customer_id"] = $res_user['id'];
        $_SESSION["response_id"] = $_POST['razorpay_order_id'];
    }

    echo '<script>window.location.href="../thankyou.php"</script>';

} else {

    $sel = dbQuery("SELECT * FROM tabl_order WHERE response_id='" . $_POST['razorpay_order_id'] . "'");
    $res = dbFetchAssoc($sel);


    $sel_user = dbQuery("SELECT * FROM tabl_customer WHERE id='" . $res['customer_id'] . "'");
    if ($res_user = dbFetchAssoc($sel_user)) {

        $_SESSION["user"] = $res_user['email'];
        $_SESSION["name"] = $res_user['name'];
        $_SESSION["customer_id"] = $res_user['id'];
        $_SESSION["response_id"] = $_POST['razorpay_order_id'];
    }


    echo '<script>window.location.href="../failed.php"</script>';
    //<p>Your payment failed</p>
    // <p>{$error}</p>';


}

//echo $html;

