<?php session_start();
include ('admin/lib/db_connection.php');
include ('admin/lib/get_functions.php'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>Deva King 777</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <!-- <link rel="stylesheet" href="xx.jpg"> -->
    <link rel="icon" href="./assets/img/logo/icon.png">
    <!--Google font-->
    <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <!--icon css-->
    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
    <link rel="stylesheet" type="text/css" href="assets/css/themify.css">
    <!--Slick slider css-->
    <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
    <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">
    <!--Animate css-->
    <link rel="stylesheet" type="text/css" href="assets/css/animate.css">
    <!-- Bootstrap css -->
    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">
    <!-- Theme css -->
    <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
    <!-- custom responsive css -->
    <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">
</head>

<body class="bg-light ">
    <!-- loader start -->
    <div class="loader-wrapper">
        <div>
            <!-- <img src="assets/images/loader.gif" alt="loader"> -->
            <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
        </div>
    </div>
    <!-- loader end -->
    <!--header start-->
    <?php include ('header.php') ?>
    <!--header end-->
    <!-- section start -->
    <section class="section-big-pt-space ratio_asos bg-light mb-30 ">
        <div class="collection-wrapper">
            <div class="custom-container">
                <div class="row">
                    <?php //include('product_filter.php'); ?>
                    <div class="collection-content col">
                        <div class="page-main-content">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="collection-product-wrapper">
                                        <div class="product-wrapper-grid">
                                            <div class="row">
                                                <?php

                                                $search = $_REQUEST['search'];
                                                if ($search == "earing") {
                                                    $search_query = "(name LIKE '%" . $_REQUEST['search'] . "%' || name LIKE '%earring%') AND";
                                                } else {
                                                    $search_query = "name LIKE '%" . $_REQUEST['search'] . "%' AND";

                                                }
                                                $sel = dbQuery("SELECT * FROM tabl_products WHERE $search_query status='1'");
                                                while ($res = dbFetchAssoc($sel)) {
                                                    // 	$sel_stock=dbQuery("SELECT * FROM tabl_stock WHERE (in_stock-out_stock>0) AND p_id='".$res['id']."'");	
                                                    //   $num=dbNumRows($sel_stock);
                                                    //  if($num>0){
                                                    $off = (($res['old_price'] - $res['price']) / $res['old_price']) * 100;
                                                    $off = round($off, 0);
                                                    ?>
                                                    <div class="col-xl-3  col-md-4 col-6  col-grid-box">
                                                        <div class="product">
                                                            <div class="product-box">
                                                                <div class="product-imgbox"> <a
                                                                        href="product-detail.php?productID=<?php echo $res['id']; ?>">
                                                                        <div class="product-front"> <img
                                                                                src="assets/images/products/<?php echo $res['image']; ?>"
                                                                                class="img-fluid" alt="product"> </div>
                                                                    </a>
                                                                    <div class="new-label">
                                                                        <div><?php echo $off ?>%</div>
                                                                    </div>
                                                                </div>
                                                                <div class="product-detail detail-center ">
                                                                    <div class="detail-title">
                                                                        <div class="detail-left"> <a
                                                                                href="product-detail.php?productID=<?php echo $res['id']; ?>">
                                                                                <h6 class="price-title">
                                                                                    <?php echo $res['name']; ?>
                                                                                </h6>
                                                                            </a> </div>
                                                                        <div class="detail-right">
                                                                            <div class="check-price"> Rs.
                                                                                <?php echo $res['old_price']; ?>
                                                                            </div>
                                                                            <div class="price">
                                                                                <div class="price"> Rs.
                                                                                    <?php echo $res['price']; ?>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <a href="product-detail.php?productID=<?php echo $res['id']; ?>"
                                                                        class="btn btn-rounded mtb-20 btn-blue">Add To Cart
                                                                    </a> <a
                                                                        href="product-detail.php?productID=<?php echo $res['id']; ?>"
                                                                        class="btn btn-rounded mtb-20">Buy Now </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php }										 //} ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- section End -->
    <!--footer-start-->
    <?php include ('footer.php') ?>
    <!--footer-end-->
    <!-- tap to top -->
    <div class="tap-top">
        <div> <i class="fa fa-angle-double-up"></i> </div>
    </div>
    <!-- tap to top End -->
    <!-- latest jquery-->
    <script src="assets/js/jquery-3.3.1.min.js"></script><!-- menu js-->
    <script src="assets/js/menu.js"></script><!-- popper js-->
    <script src="assets/js/popper.min.js"></script><!-- slick js-->
    <script src="assets/js/slick.js"></script><!-- Bootstrap js-->
    <script src="assets/js/bootstrap.js"></script><!-- Theme js-->
    <script src="assets/js/script.js"></script>
</body>
<style>
    .product .product-box:hover .product-detail.detail-center .detail-title {
        opacity: 1;
    }

    .section-big-pt-space {
        padding-top: 0px;
    }

    .mt-30 {
        margin-top: 30px;
    }

    .mb-30 {
        margin-bottom: 30px;
    }

    .mt-50 {
        margin-top: 50px;
    }

    .mtb-20 {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .btn-rounded {
        font-size: 10px !important;
        padding: 10px 11px;
        font-weight: 700;
        color: #fff !important;
        /* background-color: #cabdb4;*/
        background-color: #832729;
        border-radius: 25px;
        position: relative;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
        line-height: 1;
        display: inline-block;
        letter-spacing: 0.05em;
    }

    .btn-rounded:hover {
        background-color: #ff708a;
    }

    .instagram-box img {
        max-width: 145px;
        height: 145px;
    }

    .product .product-box {
        margin-right: 1px;
        margin-top: 15px;
        padding-right: 0px;
    }

    .product .product-box .product-imgbox img {
        margin: 0 auto;
        max-width: 100%;
        height: auto;
        /*padding: 0 35px;*/
    }

    @media (max-width: 577px) {
        .mobile-fix-option {
            position: fixed;
            bottom: 0;
            left: 0;
            height: 45px;
            background-color: #cabdb4;
        }
    }
</style>
<script>
    $(document).ready(function () {
        $(".f_sort").click(function () {
            var f_sort = '';
            $("input[name='f_sort']:checked");
            var sor_par = $(this).val();
            var url = $(location).attr('href');
            var urlParams = new URLSearchParams(window.location.search);
            //get all parametersvar
            is_param = urlParams.get('f_sort');
            if (is_param) {
                var url = new URL(url);
                var search_params = url.searchParams;
                search_params.set('f_sort', $(this).val());
                url.search = search_params.toString();
                var url = url.toString();
                var url = decodeURI(url);
                console.log(url);
            } else {
                var url = '' + url + '&f_sort=' + $(this).val() + '';
            }
            window.history.pushState('page2', 'Title', url);
            var get_url = url.split('?');
            $.ajax({
                url: 'ajax/get_filter_page.php?' + get_url[1] + '',
                type: 'get',
                beforeSend: function () {
                    $(".product-wrapper-grid").html(
                        '<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>'
                    );
                },
                success: function (data) {
                    $(".product-wrapper-grid").html(data);
                    $('html, body').animate({
                        scrollTop: $(".product-wrapper-grid").offset().top
                    }, 500);
                },
            });
        });
    });
</script>
<script>
    $(document).ready(function () {
        $(".sort_color").click(function () {
            var color_code = [];
            $.each($("input[name='color']:checked"), function () {
                color_code.push($(this).val());
            });
            if ($(this).prop("checked") == true) {
                var url = $(location).attr('href');
                var new_url = '' + url + '&color[]=' + $(this).val() + '';
                window.history.pushState('page2', 'Title', new_url);
                var get_url = new_url.split('?');
            } else {
                var url = $(location).attr('href');
                var avoid = '&color[]=' + $(this).val() + '';
                var url = url.replace(avoid, '');
                window.history.pushState('page2', 'Title', '' + url + '');
                var get_url = url.split('?');
            }
            $.ajax({
                url: 'ajax/get_filter_page.php?' + get_url[1] + '',
                type: 'get',
                beforeSend: function () {
                    $(".product-wrapper-grid").html(
                        '<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>'
                    );
                },
                success: function (data) {
                    $(".product-wrapper-grid").html(data);
                    $('html, body').animate({
                        scrollTop: $(".product-wrapper-grid").offset().top
                    }, 500);
                },
            });
        });
    });
</script>
<script>
    $(document).ready(function () {
        $(".sort_size").click(function () {
            var size_code = [];
            $.each($("input[name='size']:checked"), function () {
                size_code.push($(this).val());
            });
            if ($(this).prop("checked") == true) {
                var url = $(location).attr('href');
                var new_url = '' + url + '&size[]=' + $(this).val() + '';
                window.history.pushState('page2', 'Title', new_url);
                var get_url = new_url.split('?');
            } else {
                var url = $(location).attr('href');
                var avoid = '&size[]=' + $(this).val() + '';
                var url = url.replace(avoid, '');
                window.history.pushState('page2', 'Title', '' + url + '');
                var get_url = url.split('?');
            }
            $.ajax({
                url: 'ajax/get_filter_page.php?' + get_url[1] + '',
                type: 'get',
                beforeSend: function () {
                    $(".product-wrapper-grid").html(
                        '<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>'
                    );
                },
                success: function (data) {
                    $(".product-wrapper-grid").html(data);
                    $('html, body').animate({
                        scrollTop: $(".product-wrapper-grid").offset().top
                    }, 500);
                },
            });
        });
    });
</script>
<script>
    $(document).ready(function () {
        $(".sort_av").click(function () {
            var av_code = [];
            $.each($("input[name='availablity']:checked"), function () {
                av_code.push($(this).val());
            });
            if ($(this).prop("checked") == true) {
                var url = $(location).attr('href');
                var new_url = '' + url + '&availablity[]=' + $(this).val() + '';
                window.history.pushState('page2', 'Title', new_url);
                var get_url = new_url.split('?');
            } else {
                var url = $(location).attr('href');
                var avoid = '&availablity[]=' + $(this).val() + '';
                var url = url.replace(avoid, '');
                window.history.pushState('page2', 'Title', '' + url + '');
                var get_url = url.split('?');
            }
            $.ajax({
                url: 'ajax/get_filter_page.php?' + get_url[1] + '',
                type: 'get',
                beforeSend: function () {
                    $(".product-wrapper-grid").html(
                        '<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>'
                    );
                },
                success: function (data) {
                    $(".product-wrapper-grid").html(data);
                    $('html, body').animate({
                        scrollTop: $(".product-wrapper-grid").offset().top
                    }, 500);
                },
            });
        });
    });
</script>
<script>
    $(document).ready(function () {
        $(".sort_price").click(function () {
            var price_code = [];
            $.each($("input[name='price']:checked"), function () {
                price_code.push($(this).val());
            });
            if ($(this).prop("checked") == true) {
                var url = $(location).attr('href');
                var new_url = '' + url + '&price[]=' + $(this).val() + '';
                window.history.pushState('page2', 'Title', new_url);
                var get_url = new_url.split('?');
            } else {
                var url = $(location).attr('href');
                var avoid = '&price[]=' + $(this).val() + '';
                var url = url.replace(avoid, '');
                window.history.pushState('page2', 'Title', '' + url + '');
                var get_url = url.split('?');
            }
            $.ajax({
                url: 'ajax/get_filter_page.php?' + get_url[1] + '',
                type: 'get',
                beforeSend: function () {
                    $(".product-wrapper-grid").html(
                        '<span style="position: absolute;margin-left: 300px;margin-top: 80px;"><img src="product_loader.gif"></span>'
                    );
                },
                success: function (data) {
                    $(".product-wrapper-grid").html(data);
                    $('html, body').animate({
                        scrollTop: $(".product-wrapper-grid").offset().top
                    }, 500);
                },
            });
        });
    });
</script>

</html>