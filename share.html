<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shareable Card</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="card">
        <h2>My Shareable Card</h2>
        <p>This is a description of the card. It has some interesting content that you might want to share.</p>
        <button class="share-button" onclick="shareContent()">Share</button>
    </div>

    <script>
        function shareContent() {
            if (navigator.share) {
                navigator.share({
                    title: 'My Shareable Card',
                    text: 'Check out this amazing content!',
                    url: window.location.href,
                }).then(() => {
                    console.log('Thanks for sharing!');
                }).catch((error) => {
                    console.log('Something went wrong', error);
                });
            } else {
                alert('Web Share API not supported.');
            }
        }
    </script>
</body>
</html>
