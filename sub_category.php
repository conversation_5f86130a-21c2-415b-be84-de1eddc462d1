<?php session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>Deva King 777</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">

    <link rel="icon" href="./assets/img/logo/icon.png">

    <!--Google font-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

    <!--icon css-->
    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
    <link rel="stylesheet" type="text/css" href="assets/css/themify.css">

    <!--Slick slider css-->
    <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
    <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">

    <!--Animate css-->
    <link rel="stylesheet" type="text/css" href="assets/css/animate.css">
    <!-- Bootstrap css -->
    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">

    <!-- Theme css -->
    <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
    <!-- custom responsive css -->
    <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">

    <style>
        .myimg {
            width: 113px;
            height: 113px;
        }

        .myimg img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        .category {
            text-align: center;
            width: 113px;
            height: 113px;
            margin-bottom: 29px;
        }

        .category img {
            border-radius: 50%;
            border: 1px solid brown;
        }

        .category p {
            color: black;
            margin-top: 10px;
            font-weight: 500;
        }

        .category-container {
            display: flex;
            flex-wrap: wrap;
            /* justify-content: space-around; */
            box-shadow: 0 0 5px 1px #ff708a5e;
            border-radius: 20px;
            padding: 17px 0px;
        }

        .category-box {
            display: flex;
            justify-content: center;
            flex-basis: 16%;
            max-width: 16%;
            margin-top: 15px;

        }

        @media (max-width: 768px) {
            .category-box {
                flex-basis: 32%;
                max-width: 32%;
            }
        }

        @media (max-width: 480px) {
            .category-box {
                flex-basis: 25%;
                max-width: 48%;
            }

            .myimg {
                width: 65px;
                height: 65px;
            }

            .category {
                width: 65px;
                height: 65px;
            }

            .category p {
                color: black;
                margin-top: 10px;
                font-weight: 500;
                font-size: 12px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="head">
            <div class="d-flex p-3" style="align-items: center;">
                <a href="category.php" style="color: #777; font-size: 22px;"> <i class="fa-solid fa-arrow-left"></i></a>

                &nbsp; &nbsp;&nbsp;
                <h3>Sub Category</h3>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="category-container">
            <?php
            // Get Category Id 
            $category = $_GET['subcateoryID'];
            // listing sub categories for home
            $subCatSql = dbQuery("SELECT * FROM tabl_sub_category WHERE parent_id='$category' ORDER BY name DESC");
            if (mysqli_num_rows($subCatSql) > 0) {
                while ($subhome_cat = dbFetchAssoc($subCatSql)) {
            ?>
                    <div class="category-box">
                        <a href="product.php?cateoryID=<?= $subhome_cat['id'] ?>">
                            <div class="category">
                                <div class="myimg">
                                    <img src="assets/images/category/<?= $subhome_cat['image'] ?>" alt="<?= $subhome_cat['name'] ?>">
                                </div>
                                <p><?= $subhome_cat['name'] ?></p>
                            </div>
                        </a>
                    </div>
            <?php
                }
            }
            ?>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>

</html>