<?php 
session_start();
include('admin/lib/db_connection.php');
$page=0;
$sub_page=0;
date_default_timezone_set("Asia/Kolkata");

$sel=dbQuery("SELECT * FROM tabl_order WHERE id='6'");
$res=dbFetchAssoc($sel);

$state=dbQuery("SELECT name FROM tabl_state WHERE id='".$res['state']."'");
  $res_state=dbFetchAssoc($state);

if($res['payment_type']==1){
	$payment='Cash on Delivery';
}else{
	$payment='Online Payment';	
}

if($res['order_status_id']==0){
	$status='Pending';
}elseif($res['order_status_id']==1){
	$status='Complete';	
}elseif($res['order_status_id']==2){
	$status='Processed';	
}else{
	$status='Cancel';	
}


$to = '<EMAIL>'; // note the comma
// Subject
$subject = 'New Order';
// Message
$message ='<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/1999/REC-html401-19991224/strict.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>New Order</title>
</head>
<body style="font-family: Arial, Helvetica, sans-serif; font-size: 12px; color: #000000;">
<div style="width: 680px;"><a href="http://maddamall.com/" title="Silver Fashion"><img src="https://maddamall.com/x.jpg" alt="Silver Fashion" style="margin-bottom: 20px; border: none;" /></a>
  <table style="border-collapse: collapse; width: 100%; border-top: 1px solid #DDDDDD; border-left: 1px solid #DDDDDD; margin-bottom: 20px;">
    <thead>
      <tr>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: left; padding: 7px; color: #222222;" colspan="2">Order Details</td>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: left; padding: 7px;"><b>Order ID</b> '.$res['id'].'<br />
          <b>Order Date</b> '.$res['date_added'].'<br />
          <b>Payment Method</b> '.$payment.'<br />
        </td>
        <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: left; padding: 7px;"><b>Email</b> '.$res['email'].'<br />
          <b>Phone</b> '.$res['phone'].'<br />         
          <b>Order Status</b> '.$status.'<br /></td>
      </tr>
    </tbody>
  </table>
  <table style="border-collapse: collapse; width: 100%; border-top: 1px solid #DDDDDD; border-left: 1px solid #DDDDDD; margin-bottom: 20px;">
    <thead>
      <tr>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: left; padding: 7px; color: #222222;">Address</td>
        </tr>
    </thead>
    <tbody>
      <tr>
        <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: left; padding: 7px;">'.$res['address'].'<br/>'.$res['city'].'<br/>'.$res_state['name'].'<br/>'.$res['zipcode'].'</td>
</tr>
    </tbody>
  </table>
  <table style="border-collapse: collapse; width: 100%; border-top: 1px solid #DDDDDD; border-left: 1px solid #DDDDDD; margin-bottom: 20px;">
    <thead>
      <tr>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: left; padding: 7px; color: #222222;">Name</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: right; padding: 7px; color: #222222;">Qty</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: right; padding: 7px; color: #222222;">Unit Price</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: right; padding: 7px; color: #222222;">Total</td>
      </tr>';
	  
	  $pro_detail=dbQuery("SELECT * FROM tabl_order_product WHERE order_id='".$res['id']."'");
								   while($res_pro_detail=dbFetchAssoc($pro_detail)){
								
					 $color=dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='".$res_pro_detail['color']."'");
						$color_num=dbNumRows($color);
						  $res_color=dbFetchAssoc($color);
						  
						   $size=dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='".$res_pro_detail['size']."'");
						   $size_num=dbNumRows($size);
						  $res_size=dbFetchAssoc($size);

                   if($color_num>0){
                              $color_text='<span>Color: '.$res_color['v_value'].'</span><br/>';
				             }else{
								 $color_text='';
							 }
					if($size_num>0){
						$size_text='<span>Size: '.$res_size['v_value'].'</span><br/>';
							 }else{
								$size_text=''; 
							 }
                     $total+=$res_order_products['total'];       
						  
									   
	  
	   $message.='<tr>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD;font-weight: bold; text-align: left; padding: 7px; color: #222222;">'.$res_pro_detail['name'].'<br/>'.$color_text.' '.$size_text.'</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD;font-weight: bold; text-align: right; padding: 7px; color: #222222;">'.$res_pro_detail['qty'].'</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; font-weight: bold; text-align: right; padding: 7px; color: #222222;">'.$res_pro_detail['price'].'</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; font-weight: bold; text-align: right; padding: 7px; color: #222222;">'.$res_pro_detail['total'].'</td>
      </tr>';
	}
	$shipping=dbQuery("SELECT sum(shipping_rate) as ttlshipping FROM `tabl_order_shipping` WHERE order_id='".$res['id']."'");
                      $res_shipping=dbFetchAssoc($shipping);
					  if($res_shipping['ttlshipping']==""){
						  $shipping_rate=0;
					  }else{
						  $shipping_rate=$res_shipping['ttlshipping'];
					  }
						 
				$promo=dbQuery("SELECT sum(discount) as discount FROM `tabl_order_promo` WHERE order_id='".$res['id']."'");
                         $res_promo=dbFetchAssoc($promo);
					if($res_promo['discount']==""){
						  $discount=0;
					  }else{
						  $discount=$res_promo['discount'];
					  }	
    $message.='<tr> </thead>
  <tfoot>  
  <tr>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;" colspan="3"><b>Shipping Charges:</b></td>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;font-weight: bold;">Rs '.$shipping_rate.'</td>
    </tr>
	
	  <tr>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;" colspan="3"><b>Discount:</b></td>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;font-weight: bold;">Rs -'.$discount.'</td>
    </tr>

    <tr>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;" colspan="3"><b>Grand Total:</b></td>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;font-weight: bold;">Rs '.$res['total'].'</td>
    </tr>
  </tfoot>    
  </table> 
</div>
</body>
</html>';

// To send HTML mail, the Content-type header must be set
$headers[] = 'MIME-Version: 1.0';
$headers[] = 'Content-type: text/html; charset=iso-8859-1';

// Additional headers
$headers[] = 'From: '.SITE.' <<EMAIL>>';
//$headers[] = 'Cc: '.EMAIL.'';
// Mail it
mail($to, $subject, $message, implode("\r\n", $headers));
?>