<?php
session_start();
include('admin/lib/db_connection.php');
$page = 0;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
$today_date = date('Y-m-d H:i:s');
$session_id = session_id();
$transaction_id = uniqid();

$sel_check = dbQuery("SELECT * FROM tabl_cart WHERE session_id='" . $session_id . "'");
$res_check = dbNumRows($sel_check);

if ($res_check == 0) {
  echo '<script>window.location.href="index.php";</script>';
} else {
  dbQuery("DELETE FROM tabl_cart WHERE session_id='" . $session_id . "'");
  dbQuery("DELETE FROM tabl_cart_promo WHERE session_id='" . $session_id . "'");
  dbQuery("UPDATE tabl_order SET order_status_id=2,transaction_id='" . $transaction_id . "' WHERE id='" . $_SESSION['receipt_id'] . "'");

  $sel = dbQuery("SELECT * FROM tabl_order WHERE id='" . $_SESSION['receipt_id'] . "'");
  $res = dbFetchAssoc($sel);


  $state = dbQuery("SELECT name FROM tabl_state WHERE id='" . $res['state'] . "'");
  $res_state = dbFetchAssoc($state);

  if ($res['payment_type'] == 1) {
    $payment = 'Cash on Delivery';
  } elseif ($res['payment_type'] == 2) {
    $payment = 'Online Payment';
  } else {
    $payment = 'Wallet';
    dbQuery("UPDATE tabl_wallet SET amount=amount-'" . $_SESSION['order_total_amount'] . "' WHERE customer_id='" . $_SESSION['customer_id'] . "'");
    dbQuery("INSERT INTO tabl_wallet_details SET customer_id='" . $_SESSION['customer_id'] . "',paid_amount='" . $_SESSION['order_total_amount'] . "',transaction_id='" . $transaction_id . "',order_id='" . $_SESSION['receipt_id'] . "',date_added='" . $today_date . "'");
  }

  if ($res['order_status_id'] == 0) {
    $status = 'Pending';
  } elseif ($res['order_status_id'] == 1) {
    $status = 'Complete';
  } elseif ($res['order_status_id'] == 2) {
    $status = 'Processed';
  } elseif ($res['order_status_id'] == 3) {
    $status = 'Cancel';
  } elseif ($res['order_status_id'] == 4) {
    $status = 'Delivered';
  } else {
    $status = 'Cancel';
  }

  $to = $res['email']; // note the comma
  // Subject
  $subject = 'New Order';
  // Message
  $message = '<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/1999/REC-html401-19991224/strict.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>New Order</title>
</head>
<body style="font-family: Arial, Helvetica, sans-serif; font-size: 12px; color: #000000;">
<div style="width: 680px;"><a href="https://lokalitha_fashions.in//" title="Deva King 777"><img src="https://lokalitha_fashions.in//x.jpg" alt="Deva King 777" style="margin-bottom: 20px; border: none;" /></a>
  <table style="border-collapse: collapse; width: 100%; border-top: 1px solid #DDDDDD; border-left: 1px solid #DDDDDD; margin-bottom: 20px;">
    <thead>
      <tr>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: left; padding: 7px; color: #222222;" colspan="2">Order Details</td>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: left; padding: 7px;"><b>Order ID</b> ' . $res['id'] . '<br />
          <b>Order Date</b> ' . $res['date_added'] . '<br />
          <b>Payment Method</b> ' . $payment . '<br />
        </td>
        <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: left; padding: 7px;"><b>Email</b> ' . $res['email'] . '<br />
          <b>Phone</b> ' . $res['phone'] . '<br />         
          <b>Order Status</b> ' . $status . '<br /></td>
      </tr>
    </tbody>
  </table>
  <table style="border-collapse: collapse; width: 100%; border-top: 1px solid #DDDDDD; border-left: 1px solid #DDDDDD; margin-bottom: 20px;">
    <thead>
      <tr>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: left; padding: 7px; color: #222222;">Address</td>
        </tr>
    </thead>
    <tbody>
      <tr>
        <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: left; padding: 7px;">' . $res['address'] . '<br/>' . $res['city'] . '<br/>' . $res_state['name'] . '<br/>' . $res['zipcode'] . '</td>
</tr>
    </tbody>
  </table>
  <table style="border-collapse: collapse; width: 100%; border-top: 1px solid #DDDDDD; border-left: 1px solid #DDDDDD; margin-bottom: 20px;">
    <thead>
      <tr>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: left; padding: 7px; color: #222222;">Name</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: right; padding: 7px; color: #222222;">Qty</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: right; padding: 7px; color: #222222;">Unit Price</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: right; padding: 7px; color: #222222;">Total</td>
      </tr>';

  $pro_detail = dbQuery("SELECT * FROM tabl_order_product WHERE order_id='" . $res['id'] . "'");
  while ($res_pro_detail = dbFetchAssoc($pro_detail)) {

    $color = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_pro_detail['color'] . "'");
    $color_num = dbNumRows($color);
    $res_color = dbFetchAssoc($color);

    $size = dbQuery("SELECT tabl_product_options.*,tabl_product_variation_value.* FROM tabl_product_options INNER JOIN tabl_product_variation_value ON tabl_product_variation_value.id=tabl_product_options.v_id WHERE tabl_product_options.id='" . $res_pro_detail['size'] . "'");
    $size_num = dbNumRows($size);
    $res_size = dbFetchAssoc($size);

    if ($color_num > 0) {
      $color_text = '<span>Color: ' . $res_color['v_value'] . '</span><br/>';
    } else {
      $color_text = '';
    }
    if ($size_num > 0) {
      $size_text = '<span>Size: ' . $res_size['v_value'] . '</span><br/>';
    } else {
      $size_text = '';
    }
    $total += $res_order_products['total'];



    $message .= '<tr>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD;font-weight: bold; text-align: left; padding: 7px; color: #222222;">' . $res_pro_detail['name'] . '<br/>' . $color_text . ' ' . $size_text . '</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD;font-weight: bold; text-align: right; padding: 7px; color: #222222;">' . $res_pro_detail['qty'] . '</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; font-weight: bold; text-align: right; padding: 7px; color: #222222;">' . $res_pro_detail['price'] . '</td>
        <td style="font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; font-weight: bold; text-align: right; padding: 7px; color: #222222;">' . $res_pro_detail['total'] . '</td>
      </tr>';
  }
  $shipping = dbQuery("SELECT sum(shipping_rate) as ttlshipping FROM `tabl_order_shipping` WHERE order_id='" . $res['id'] . "'");
  $res_shipping = dbFetchAssoc($shipping);
  if ($res_shipping['ttlshipping'] == "") {
    $shipping_rate = 0;
  } else {
    $shipping_rate = $res_shipping['ttlshipping'];
  }

  $promo = dbQuery("SELECT sum(discount) as discount FROM `tabl_order_promo` WHERE order_id='" . $res['id'] . "'");
  $res_promo = dbFetchAssoc($promo);
  if ($res_promo['discount'] == "") {
    $discount = 0;
  } else {
    $discount = $res_promo['discount'];
  }
  $message .= '<tr> </thead>
  <tfoot>  
  <tr>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;" colspan="3"><b>Shipping Charges:</b></td>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;font-weight: bold;">Rs ' . $shipping_rate . '</td>
    </tr>
	
	  <tr>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;" colspan="3"><b>Discount:</b></td>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;font-weight: bold;">Rs -' . $discount . '</td>
    </tr>

    <tr>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;" colspan="3"><b>Grand Total:</b></td>
      <td style="font-size: 12px;	border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;font-weight: bold;">Rs ' . $res['total'] . '</td>
    </tr>
  </tfoot>    
  </table> 
</div>
</body>
</html>';

  // To send HTML mail, the Content-type header must be set
  $headers[] = 'MIME-Version: 1.0';
  $headers[] = 'Content-type: text/html; charset=iso-8859-1';

  // Additional headers
  $headers[] = 'From: ' . SITE . ' <' . EMAIL . '>';
  $headers[] = 'Cc: ' . EMAIL . '';
  // Mail it
  mail($to, $subject, $message, implode("\r\n", $headers));

  unset($_SESSION['receipt_id']);
  unset($_SESSION['order_name']);
  unset($_SESSION['order_pincode']);
  unset($_SESSION['order_city']);
  unset($_SESSION['order_state']);
  unset($_SESSION['order_address']);
  unset($_SESSION['order_email']);
  unset($_SESSION['order_phone']);
  unset($_SESSION['order_total_amount']);
  unset($_SESSION['order_payment_type']);

?>
  <!DOCTYPE html>
  <html lang="en">

  <head>

    <title>Deva King 777</title>

    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <meta name="viewport" content="width=device-width,initial-scale=1">

    <link rel="icon" href="./assets/img/logo/icon.png">
    <!--Google font-->

    <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">





    <!--icon css-->

    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">

    <link rel="stylesheet" type="text/css" href="assets/css/themify.css">



    <!--Slick slider css-->

    <link rel="stylesheet" type="text/css" href="assets/css/slick.css">

    <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">



    <!--Animate css-->

    <link rel="stylesheet" type="text/css" href="assets/css/animate.css">

    <!-- Bootstrap css -->

    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">



    <!-- Theme css -->

    <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">


    <style>
      .thank_gif {
        /* width: 400px; */
        width: 100%;
      }
    </style>
  </head>

  <body class="bg-light ">



    <!-- loader start -->

    <div class="loader-wrapper">
      <div>
        <!-- <img src="assets/images/loader.gif" alt="loader"> -->
        <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
      </div>
    </div>

    <!-- loader end -->



    <!--header start-->

    <?php include('header.php') ?>

    <!--header end-->





    <!-- thank-you section start -->

    <section class="section-big-py-space">

      <div class="container">

        <div class="row">

          <div class="col-md-12 text-center">



            <!-- <canvas width="500" height="500" style="width: 250px;touch-action: none;margin-top: -65px;"></canvas> -->



            <!-- ////////////////////////////////// THANK YOU ////////////////////////////////// -->

            <div id="box1" class="container">
              <img src="./assets/img/thankyou.gif" alt="" class="thank_gif">

            </div>

            <!-- ////////////////////////////////// SHADOW ////////////////////////////////// -->


            <div class="success-text" style="margin-top: 300px;">





              <!--<i class="fa fa-check-circle" aria-hidden="true"></i>

                   <h2>thank you</h2>-->









              <p> Your order is successfully placed </p>

              <p>Transaction ID:<?php echo $transaction_id; ?></p>

            </div>

          </div>

        </div>

      </div>

    </section>

    <!-- Section ends -->





    <!--footer-start-->



    <?php include('footer.php') ?>

    <!--footer-end-->







    <!-- tap to top -->

    <div class="tap-top">

      <div>

        <i class="fa fa-angle-double-up"></i>

      </div>

    </div>

    <!-- tap to top End -->





    <!-- latest jquery-->

    <script src="assets/js/jquery-3.3.1.min.js"></script>



    <!-- menu js-->

    <script src="assets/js/menu.js"></script>



    <!-- popper js-->

    <script src="assets/js/popper.min.js"></script>



    <!-- slick js-->

    <script src="assets/js/slick.js"></script>



    <!-- Bootstrap js-->

    <script src="assets/js/bootstrap.js"></script>



    <!-- Theme js-->

    <script src="assets/js/script.js"></script>



    <script src="https://unpkg.com/zdog@1/dist/zdog.dist.min.js"></script>



    <script src="https://unpkg.com/animejs@3.0.1/lib/anime.min.js"></script>



    <script>
      // from the Zdog object extract the necessary modules

      const {

        Illustration,
        Ellipse,
        Rect,
        Shape,
        Group,
        Anchor,

      } = Zdog;



      // set up the illustration within the existing canvas element

      const illustration = new Illustration({

        element: 'canvas',

        dragRotate: true,

      });



      // below the star draw a circle with a fill and no stroke, for the shadow

      const shadow = new Ellipse({

        addTo: illustration,

        diameter: 100,

        stroke: false,

        fill: true,

        color: 'hsla(45, 100%, 58%, 0.4)',

        translate: {
          x: 50,
          y: 100
        },

        rotate: {
          x: Math.PI / 1.7
        },

      });



      // include an anchor point for the star

      // ! position the star atop the anchor, to have the rotation occur around this point

      const starAnchor = new Anchor({

        addTo: illustration,

        translate: {
          y: 100
        },

        rotate: {
          z: Math.PI / 10
        },

      });



      // draw a star in a group element positioned atop the anchor point

      const starGroup = new Group({

        addTo: starAnchor,

        translate: {
          x: -70,
          y: -170
        }, // -70 to center the 140 wide shape

      });



      // draw the path describing the star

      new Shape({

        addTo: starGroup,

        path: [

          {
            x: 0,
            y: 45
          },

          {
            x: 45,
            y: 45
          },

          {
            x: 70,
            y: 0
          },

          {
            x: 95,
            y: 45
          },

          {
            x: 140,
            y: 45
          },

          {
            x: 105,
            y: 80
          },

          {
            x: 120,
            y: 130
          },

          {
            x: 70,
            y: 105
          },

          {
            x: 20,
            y: 130
          },

          {
            x: 35,
            y: 80
          },

          {
            x: 0,
            y: 45
          },

        ],

        stroke: 40,

        color: 'hsl(45, 100%, 58%)',

      });

      // within the path include a rectangle to remove the gap between the center of the star and its stroke

      new Rect({

        addTo: starGroup,

        width: 40,

        height: 50,

        stroke: 40,

        translate: {
          x: 70,
          y: 70
        },

        color: 'hsl(45, 100%, 58%)',

      });



      // include a group for the eyes, positioned halfway through the height of the star

      const eyesGroup = new Group({

        addTo: starGroup,

        translate: {
          x: 70,
          y: 72.5,
          z: 20
        },

      });



      // add black circles describing the contour of the eyes, and either end of the star

      const eye = new Ellipse({

        addTo: eyesGroup,

        diameter: 5,

        stroke: 15,

        translate: {
          x: -32.5
        },

        color: 'hsl(0, 0%, 0%)',

      });

      eye.copy({

        translate: {
          x: 32.5
        },

      });



      // add an anchor point for the white part of the eyes

      // by later translating the white part of the eyes, the rotation allows to have the circle rotate around the anchor point

      const leftEyeAnchor = new Anchor({

        addTo: eyesGroup,

        translate: {
          x: -32.5,
          z: 0.5
        },

      });

      const leftEye = new Ellipse({

        addTo: leftEyeAnchor,

        diameter: 1,

        stroke: 5,

        color: 'hsl(0, 100%, 100%)',

        translate: {
          x: -3.5
        },

      });



      // copy the left anchor for the right side

      const rightEyeAnchor = leftEyeAnchor.copyGraph({

        translate: {
          x: 32.5,
          z: 0.5
        },

      });



      // include an anchor point for the mouth

      // by centering the mouth around the anchor and scaling the anchor itself, the change in size occurs from the center of the mouth

      const mouthAnchor = new Anchor({

        addTo: starGroup,

        translate: {
          x: 70,
          y: 95,
          z: 20
        },

        scale: 0.8,

      });

      // draw a mouth with a line and arc commands

      const mouth = new Shape({

        addTo: mouthAnchor,

        path: [

          {
            x: -8,
            y: 0
          },

          {
            x: 8,
            y: 0
          },

          {

            arc: [

              {
                x: 4,
                y: 6
              },

              {
                x: 0,
                y: 6
              },

            ],

          },

          {

            arc: [

              {
                x: -4,
                y: 6
              },

              {
                x: -8,
                y: 0
              },

            ],

          },

        ],

        stroke: 10,

        color: 'hsl(358, 100%, 65%)',

      });



      illustration.updateRenderGraph();



      /* to animate the star, change the transform property as follows



      |variableName|transform|valueRange|

      |---|---|---|

      |starAnchor|rotate.z|[Math.PI/10, -Math.PI/10]|

      |leftIrisAnchor && rightIrisAnchor|rotate.z|[0, Math.PI/2]|

      |mouthAnchor|scale|[0.8, 1.2]|

      |shadow|translate.x|[50, -50]|

      */



      // ! I am positive there are much better ways to achieve this animation, but this is my take using anime.js

      // I am still a newbie when it comes to animation

      // create an object describing the values for the different elements

      const starObject = {

        star: Math.PI / 10,

        shadow: 50,

        mouth: 0.8,

        eyes: 0

      }



      // set up a repeating animation which constantly updates the illustration and updates the desired transform properties according to the object's values

      const timeline = anime.timeline({

        duration: 1100,

        easing: 'easeInOutQuart',

        direction: 'alternate',

        loop: true,

        update: () => {

          starAnchor.rotate.z = starObject.star;

          shadow.translate.x = starObject.shadow;

          mouth.scale = starObject.mouth;

          leftEyeAnchor.rotate.z = starObject.eyes;

          rightEyeAnchor.rotate.z = starObject.eyes;



          illustration.updateRenderGraph();

        }

      });



      // animate the star with a slightly more pronounced easing function

      timeline.add({

        targets: starObject,

        star: -Math.PI / 10,

        easing: 'easeInOutQuint',

      });

      // have the shadow follow with a small delay

      timeline.add({

        targets: starObject,

        delay: 20,

        shadow: -50,

      }, '-=1100')



      // with a smaller duration and slightly postponed, animate the mouth and the eyes

      timeline.add({

        targets: starObject,

        mouth: 1.2,

        duration: 300,

      }, '-=800');



      timeline.add({

        targets: starObject,

        eyes: Math.PI / 2,

        duration: 900,

      }, '-=1000');
    </script>



  </body>

  <style>
    .product .product-box:hover .product-detail.detail-center .detail-title {

      opacity: 1;

    }

    .section-big-pt-space {

      padding-top: 0px;

    }



    .mt-30 {

      margin-top: 30px;

    }



    .mb-30 {

      margin-bottom: 30px;

    }



    .mt-50 {

      margin-top: 50px;

    }



    .mtb-20 {

      margin-top: 20px;

      margin-bottom: 20px;

    }





    .btn-rounded {

      font-size: 13px;

      padding: 10px 11px;

      font-weight: 700;

      color: #fff !important;

      /* background-color: #cabdb4;*/

      background-color: #ff708a;

      border-radius: 25px;

      position: relative;

      -webkit-transition: all 0.3s;

      transition: all 0.3s;

      line-height: 1;

      display: inline-block;

      letter-spacing: 0.05em;

    }



    .btn-rounded:hover {

      background-color: #ff708a;

    }



    .instagram-box img {

      max-width: 145px;

      height: 145px;

    }



    .product .product-box {

      margin-right: 15px;

      margin-top: 15px;

      padding-right: 0px;

    }



    .product .product-box .product-imgbox img {

      margin: 0 auto;

      max-width: 100%;

      height: auto;

      padding: 0 35px;

    }

    .section-big-py-space {

      padding-bottom: 50px;

      padding-top: 0px;

    }











    #box1 {

      height: 350px;

      left: 50%;

      position: absolute;

      top: 50%;

      transform: translate(-50%, -50%);

      width: 350px;

    }

    .you {

      left: 50%;

      position: absolute;

      transform: translateX(-50%);

    }

    #box1 .st0 {

      fill: none;

      stroke: #CF002F;

      stroke-width: 6;

      stroke-linecap: round;

      stroke-linejoin: round;

      stroke-miterlimit: 10;

    }

    .thank .st0 {

      stroke-dashoffset: 2000;

      stroke-dasharray: 2000;

      animation: dash1 2.5s ease-in;

      animation-fill-mode: forwards;

      animation-direction: reverse;

    }

    .you .st0 {

      stroke-dashoffset: 2000;

      stroke-dasharray: 2000;

      animation: dash1 2.5s ease-in;

      animation-fill-mode: forwards;

      animation-direction: reverse;

      animation-delay: 2s;

    }

    @keyframes dash1 {

      from {
        stroke-dashoffset: 0;
      }

      to {
        stroke-dashoffset: 2000;
      }

    }

    /* /////////////////////////////////////////// shadow //////////*/

    #box2 {

      height: 350px;

      left: calc(50% + 6px);

      position: absolute;

      top: 50%;

      transform: translate(-50%, -50%);

      width: 350px;

      z-index: -1;

    }

    #box2 .st0 {

      fill: none;

      stroke: #610018;

      stroke-width: 6;

      stroke-linecap: round;

      stroke-linejoin: round;

      stroke-miterlimit: 10;

    }

    /* /////////////////////////////////////////// circle //////////*/

    #box3 {

      height: 350px;

      left: 50%;

      position: absolute;

      top: 50%;

      transform: translate(-50%, -50%);

      width: 350px;

    }

    #box3 .st0 {

      animation: circle-anima 15s infinite linear;

      transform-origin: center;

      fill: none;

      opacity: 0;

      stroke: #CF002F;

      stroke-width: 4;

      stroke-linecap: round;

      stroke-linejoin: round;

      stroke-dasharray: 5.0035, 15.0104;

      z-index: -5;

    }

    @keyframes circle-anima {

      0% {}

      10% {
        opacity: 1
      }

      100% {
        transform: rotate(360deg)
      }

    }

    /* /////////////////////////////////////////// credit //////////*/

    #link {

      bottom: 20px;

      color: #CF002F;

      opacity: 0.6;

      display: flex;

      align-items: center;

      position: absolute;

      left: 50%;

      transform: translateX(-50%);

    }

    #link p {
      font-size: 20px;
      margin: 0;
      margin-left: 5px;
    }

    #link:hover {
      opacity: 1;
    }





    .success-text p {

      text-transform: capitalize;

      font-size: calc(16px + (18 - 14) * ((100vw - 320px) / (1920 - 320)));

      font-weight: 900;

      color: #000;

    }
  </style>

  </html>
<?php } ?>