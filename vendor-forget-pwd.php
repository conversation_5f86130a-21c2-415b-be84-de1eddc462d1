<?php 
session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
//include('auth.php');
$page=1;
$sub_page=0;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d');
?>
<!DOCTYPE html>

<html lang="en">



<head>

  <title>Deva King 777</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <link rel="icon" href="./assets/img/logo/icon.png">

  <!--Google font-->

  <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">

  <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">

  

  

	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

 



  <!--icon css-->

  <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">

  <link rel="stylesheet" type="text/css" href="assets/css/themify.css">



  <!--Slick slider css-->

  <link rel="stylesheet" type="text/css" href="assets/css/slick.css">

  <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">



  <!--Animate css-->

  <link rel="stylesheet" type="text/css" href="assets/css/animate.css">

  <!-- Bootstrap css -->

  <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">



  <!-- Theme css -->

  <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
  <!-- custom responsive css -->
  <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">

</head>

<body class="bg-light ">



<!-- loader start -->

<div class="loader-wrapper">
        <div>
            <!-- <img src="assets/images/loader.gif" alt="loader"> -->
            <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
        </div>
    </div>

<!-- loader end -->



<!--header start-->



   <?php include('header.php')?>

<!--header end-->





<!--section start-->

<section class="login-page pwd-page section-big-py-space bg-light">

    <div class="container">

        <div class="row">

            <div class="col-lg-6 offset-lg-3">

                <div class="theme-card">

                <h3>Forget Your Password</h3>

				

					<img src="assets/images/Difference.gif" class="img-fluid  " alt="logo">

                <form class="theme-form" id="forget_password">

                    <div class="form-row">

                        <div class="col-md-12">

                          <div class="form-group">

                              <input type="text" class="form-control" name="email" id="email" placeholder="Enter your email id" required="">

                          </div>

                        </div> <button type="submit" id="submit" class="btn btn-normal">Submit</button>

                    </div>
<div class="loader_login"></div>
                </form>

                </div>

            </div>

        </div>

    </div>

</section>

<!--Section ends-->





<!--footer-start-->



   <?php include('footer.php')?>

<!--footer-end-->





<!-- tap to top -->

<div class="tap-top">

    <div>

        <i class="fa fa-angle-double-up"></i>

    </div>

</div>

<!-- tap to top End -->







<!-- latest jquery-->

<script src="assets/js/jquery-3.3.1.min.js" ></script>



<!-- menu js-->

<script src="assets/js/menu.js"></script>



<!-- popper js-->

<script src="assets/js/popper.min.js" ></script>



<!-- slick js-->

<script  src="assets/js/slick.js"></script>



<!-- Bootstrap js-->

<script src="assets/js/bootstrap.js" ></script>



<!-- Theme js-->

<script src="assets/js/script.js" ></script>

</body>

</html>
<script>
 $("#forget_password").submit(function(e) {
	$("#submit").attr('disabled',true);
	$(".loader_login").html('<img src="loader.gif"></i> please wait...');	
 $.ajax({
	  url:'ajax/vendor_forget_password.php',
	  type:'post',
	  data:$("#forget_password").serialize(),
	  success:function(data){
	if(data==1)
		{     
	$("#submit").attr('disabled',false);
	  $(".loader_login").html('<div class="alert alert-success" role="alert">Your login details sent to your registered Email address!</div>');	
	setTimeout(function() {
      $(".loader_login").html('');	 
					}, 8000);	
		}
	else{
	  $("#submit").attr('disabled',false);
	  $(".loader_login").html('<div class="alert alert-danger" role="alert">No Email address exist.</div>');	
	setTimeout(function() {
      $(".loader_login").html('');	 
					}, 8000);	

			}
		},
	  });
   e.preventDefault(); // avoid to execute the actual submit of the form.
});
</script>
