<?php 
session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
//include('auth.php');
$page=1;
$sub_page=0;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d');
?>
<!DOCTYPE html>

<html lang="en">



<head>

  <title>Deva King 777</title>

  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <meta name="viewport" content="width=device-width,initial-scale=1">
  <link rel="icon" href="./assets/img/logo/icon.png">

  <!--Google font-->

  <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">

  <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">

  

  

	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

 



  <!--icon css-->

  <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">

  <link rel="stylesheet" type="text/css" href="assets/css/themify.css">



  <!--Slick slider css-->

  <link rel="stylesheet" type="text/css" href="assets/css/slick.css">

  <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">



  <!--Animate css-->

  <link rel="stylesheet" type="text/css" href="assets/css/animate.css">

  <!-- Bootstrap css -->

  <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">



  <!-- Theme css -->

  <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
  <!-- custom responsive css -->
  <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">

</head>

<body class="bg-light ">



<!-- loader start -->

<div class="loader-wrapper">
        <div>
            <!-- <img src="assets/images/loader.gif" alt="loader"> -->
            <video src="./assets/images/VIDEO/20240223_1641361 (2).mp4"></video>
        </div>
    </div>
<!-- loader end -->



<!--header start-->



   <?php include('header.php')?>

<!--header end-->



<!--section start-->

<section class="login-page section-big-py-space bg-light">

    <div class="custom-container">

        <div class="row">

            <div class="col-lg-6 offset-lg-3">

                <div class="theme-card">

                    <h3 class="text-center">Vendor Create account </h3>

					<img src="assets/images/Difference.gif" class="img-fluid  " alt="logo">

                    <form class="theme-form" id="signform" method="post">

                        <div class="form-row">

                            <div class="col-md-12 form-group">

                                <label for="name"> Business Name<span class="red">*</span></label>

                                <input type="text" class="form-control" id="name" name="name" placeholder="" required="">

                            </div>

                            <div class="col-md-12 form-group">

                                <label for="type of business">Type of Business</label>

                                <input type="text" class="form-control" name="type" id="type of business" placeholder="">

                            </div>

                        </div>

                        <div class="form-row">

                            <div class="col-md-12 form-group">

                                <label for="contact">Contact No<span class="red">*</span></label>

                                <input type="number" class="form-control" name="phone" id="phone" onkeyup="check_phone(this.value);" maxlength="10" autocomplete="contact" required>
    <span class="phone_loader"></span>

                            </div>

                            <div class="col-md-12 form-group">

                                <label for="email">Email<span class="red">*</span></label>
<input type="email" class="form-control" name="email" id="email" onkeyup="check_email(this.value);" autocomplete="user-name"  required>
    <span class="email_loader"></span>
                            </div>

							<div class="col-md-12 form-group">

                                <label for="location">Business Location </label>

                                <input type="text" class="form-control" name="location" id="location" placeholder="">

                            </div>

							

							<div class="col-md-12 form-group">

                                <label for="gst">GST or Adhar Number<span class="red">*</span></label>

                                <input type="text" class="form-control" name="gst_adhaar" id="gst_adhaar" placeholder="" required="">

                            </div>

							

							<div class="col-md-12 form-group">

                                <label for="location">Address of business </label>

                                <input type="text" class="form-control" name="address" id="location" placeholder="">

                            </div>
                            
                                      <div class="col-md-12 form-group">

                                <label for="review">Password</label>
							
								<input type="password" name="password" class="form-control" id="password"  autocomplete="new-password"  required>

                            </div>

							<div class="col-md-12 form-group">

                                <label for="review">Confirm Password</label>

								
								 <input type="password" name="confirm_password" class="form-control" id="confirm_password" autocomplete="new-password" required>

                            </div>


                            <div class="col-md-12 form-group">
                            <button type="submit" class="btn btn-normal">create Account</button>
                           </div>

                        </div>
<div class="loader_login"></div>
                        <div class="form-row">

                            <div class="col-md-12 ">

                                <p >Have you already account? <a href="vendor-login.php" class="txt-default">click</a> here to &nbsp;<a href="vendor-login.php" class="txt-default">Login</a></p>

                            </div>

                        </div>

                    </form>

                </div>

            </div>

        </div>

    </div>

</section>

<!--Section ends-->







<!--footer-start-->



   <?php include('footer.php')?>

<!--footer-end-->





<!-- tap to top -->

<div class="tap-top">

    <div>

        <i class="fa fa-angle-double-up"></i>

    </div>

</div>

<!-- tap to top End -->



<!-- latest jquery-->

<script src="assets/js/jquery-3.3.1.min.js" ></script>



<!-- menu js-->

<script src="assets/js/menu.js"></script>



<!-- popper js-->

<script src="assets/js/popper.min.js" ></script>



<!-- slick js-->

<script  src="assets/js/slick.js"></script>



<!-- Bootstrap js-->

<script src="assets/js/bootstrap.js" ></script>



<!-- Theme js-->

<script src="assets/js/script.js" ></script>



</body>

<style>

.red {

    color: red;

}

</style>



</html>
<script>
 $("#signform").submit(function(e) {
	$("#submit").attr('disabled',true);
	$(".loader_login").html('<img src="loader.gif"></i> please wait...');	
 $.ajax({
	  url:'ajax/vendor_signup.php',
	  type:'post',
	  data:$("#signform").serialize(),
	  success:function(data){
	if(data==1)
		{     
		  $(".loader_login").html('<div class="alert alert-success" role="alert">Registered successfully!</div>');	
		}
	else{
	  $("#submit").attr('disabled',false);
	  $(".loader_login").html('<div class="alert alert-danger" role="alert">Email Address OR Phone no is already Exist!</div>');	
	setTimeout(function() {
      $(".loader_login").html('');	 
					}, 8000);	

			}
		},
	  });
   e.preventDefault(); // avoid to execute the actual submit of the form.
});
</script>

<script>
function check_email(email){
	if(email!=""){
	$(".email_loader").html('<img src="loader.gif" width="25"></i> check availablity...');	
 $.ajax({
	  url:'ajax/check_vendor_email.php',
	  type:'post',
	  data:{'email':email},
	  success:function(data){
	if(data==1)
		{     
		 $(".email_loader").html('<span style="color:green">This email address is Available!</span>');		
		}
	else{
	  $(".email_loader").html('<span style="color:red">This email is Already Exist!</span>');	
			}
		},
	  });
	}else{
		$(".email_loader").html('');	
	} 
	}
</script>

<script>
function check_phone(phone){
	if(phone!=""){
	$(".phone_loader").html('<img src="loader.gif" width="25"></i> check availablity...');	
 $.ajax({
	  url:'ajax/check_vendor_phone.php',
	  type:'post',
	  data:{'phone':phone},
	  success:function(data){
	if(data==1)
		{     
		 $(".phone_loader").html('<span style="color:green">This contact no. is Available!</span>');		
		}
	else{
	  $(".phone_loader").html('<span style="color:red">This Contact no is Already Exist!</span>');	
			}
		},
	  });
	}else{
	  $(".phone_loader").html('');		
	}
}
</script>

<script>
var password = document.getElementById("password")
  , confirm_password = document.getElementById("confirm_password");

function validatePassword(){
  if(password.value != confirm_password.value) {
    confirm_password.setCustomValidity("Passwords Don't Match");
  } else {
    confirm_password.setCustomValidity('');
  }
}

password.onchange = validatePassword;
confirm_password.onkeyup = validatePassword;

</script>
