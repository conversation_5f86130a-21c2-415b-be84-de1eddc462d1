<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'PhonePe\\Env' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/Env.php',
    'PhonePe\\common\\configs\\Constants' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/configs/Constants.php',
    'PhonePe\\common\\configs\\Headers' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/configs/Headers.php',
    'PhonePe\\common\\configs\\Instruments' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/configs/Instruments.php',
    'PhonePe\\common\\configs\\MerchantConfig' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/configs/MerchantConfig.php',
    'PhonePe\\common\\configs\\Rails' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/configs/Rails.php',
    'PhonePe\\common\\eventHandler\\Event' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/eventHandler/Event.php',
    'PhonePe\\common\\eventHandler\\EventPublisher' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/eventHandler/EventPublisher.php',
    'PhonePe\\common\\exceptions\\PhonePeException' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/exceptions/PhonePeException.php',
    'PhonePe\\common\\tokenHandler\\OAuthToken' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/tokenHandler/OAuthToken.php',
    'PhonePe\\common\\tokenHandler\\TokenService' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/tokenHandler/TokenService.php',
    'PhonePe\\common\\utils\\CurlHttpClient' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/utils/CurlHttpClient.php',
    'PhonePe\\common\\utils\\HttpRequest' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/utils/HttpRequest.php',
    'PhonePe\\common\\utils\\HttpResponse' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/utils/HttpResponse.php',
    'PhonePe\\payments\\v2\\models\\request\\StandardCheckoutPayRequest' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/request/StandardCheckoutPayRequest.php',
    'PhonePe\\payments\\v2\\models\\request\\StandardCheckoutRefundRequest' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/request/StandardCheckoutRefundRequest.php',
    'PhonePe\\payments\\v2\\models\\request\\builders\\StandardCheckoutPayRequestBuilder' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/request/builders/StandardCheckoutPayRequestBuilder.php',
    'PhonePe\\payments\\v2\\models\\request\\builders\\StandardCheckoutRefundRequestBuilder' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/request/builders/StandardCheckoutRefundRequestBuilder.php',
    'PhonePe\\payments\\v2\\models\\response\\CallbackResponse' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/CallbackResponse.php',
    'PhonePe\\payments\\v2\\models\\response\\RefundStatusCheckResponse' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/RefundStatusCheckResponse.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\AccountInstrument' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/AccountInstrument.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\CreditCardInstrument' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/CreditCardInstrument.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\CreditLinePaymentInstrument' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/CreditLinePaymentInstrument.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\DebitCardInstrument' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/DebitCardInstrument.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\EgvPaymentInstrument' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/EgvPaymentInstrument.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\Instrument' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/Instrument.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\MetaInfo' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/MetaInfo.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\NetBankingInstrument' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/NetBankingInstrument.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\Payload' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/Payload.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\PaymentDetail' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/PaymentDetail.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\PgRail' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/PgRail.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\PpiEgvRail' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/PpiEgvRail.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\PpiWalletRail' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/PpiWalletRail.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\Rail' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/Rail.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\UpiRail' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/UpiRail.php',
    'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\WalletPaymentInstrument' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/WalletPaymentInstrument.php',
    'PhonePe\\payments\\v2\\models\\response\\StandardCheckoutPayResponse' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/StandardCheckoutPayResponse.php',
    'PhonePe\\payments\\v2\\models\\response\\StandardCheckoutRefundResponse' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/StandardCheckoutRefundResponse.php',
    'PhonePe\\payments\\v2\\models\\response\\StatusCheckResponse' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/StatusCheckResponse.php',
    'PhonePe\\payments\\v2\\standardCheckout\\StandardCheckoutClient' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/standardCheckout/StandardCheckoutClient.php',
    'PhonePe\\payments\\v2\\standardCheckout\\StandardCheckoutConstants' => $vendorDir . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/standardCheckout/StandardCheckoutConstants.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'Unit\\EnvTest' => $vendorDir . '/phonepe/pg-php-sdk-v2/tests/Unit/EnvTest.php',
    'Unit\\common\\TokenHandler\\TokenServiceTest' => $vendorDir . '/phonepe/pg-php-sdk-v2/tests/Unit/common/TokenHandler/TokenServiceTest.php',
    'Unit\\common\\utils\\HttpRequestTest' => $vendorDir . '/phonepe/pg-php-sdk-v2/tests/Unit/common/utils/HttpRequestTest.php',
    'Unit\\common\\utils\\RequestGeneratorTest' => $vendorDir . '/phonepe/pg-php-sdk-v2/tests/Unit/common/utils/RequestGeneratorTest.php',
    'Unit\\payments\\v2\\standardCheckout\\StandardCheckoutClientTest' => $vendorDir . '/phonepe/pg-php-sdk-v2/tests/Unit/payments/v2/standardCheckout/StandardCheckoutClientTest.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
);
