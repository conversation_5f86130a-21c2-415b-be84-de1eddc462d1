<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitd74dfbc9727ce4532999700b6359c1e1
{
    public static $files = array (
        '320cde22f66dd4f5d3fd621d3e88b98f' => __DIR__ . '/..' . '/symfony/polyfill-ctype/bootstrap.php',
        '0e6d7bf4a5811bfa5cf40c5ccd6fae6a' => __DIR__ . '/..' . '/symfony/polyfill-mbstring/bootstrap.php',
        'a4a119a56e50fbb293281d9a48007e0e' => __DIR__ . '/..' . '/symfony/polyfill-php80/bootstrap.php',
    );

    public static $prefixLengthsPsr4 = array (
        'S' => 
        array (
            'Symfony\\Polyfill\\Php80\\' => 23,
            'Symfony\\Polyfill\\Mbstring\\' => 26,
            'Symfony\\Polyfill\\Ctype\\' => 23,
        ),
        'P' => 
        array (
            'PhpOption\\' => 10,
        ),
        'G' => 
        array (
            'GrahamCampbell\\ResultType\\' => 26,
        ),
        'D' => 
        array (
            'Dotenv\\' => 7,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Symfony\\Polyfill\\Php80\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-php80',
        ),
        'Symfony\\Polyfill\\Mbstring\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-mbstring',
        ),
        'Symfony\\Polyfill\\Ctype\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-ctype',
        ),
        'PhpOption\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpoption/phpoption/src/PhpOption',
        ),
        'GrahamCampbell\\ResultType\\' => 
        array (
            0 => __DIR__ . '/..' . '/graham-campbell/result-type/src',
        ),
        'Dotenv\\' => 
        array (
            0 => __DIR__ . '/..' . '/vlucas/phpdotenv/src',
        ),
    );

    public static $prefixesPsr0 = array (
        'J' => 
        array (
            'JsonMapper' => 
            array (
                0 => __DIR__ . '/..' . '/netresearch/jsonmapper/src',
            ),
        ),
    );

    public static $classMap = array (
        'Attribute' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'PhonePe\\Env' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/Env.php',
        'PhonePe\\common\\configs\\Constants' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/configs/Constants.php',
        'PhonePe\\common\\configs\\Headers' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/configs/Headers.php',
        'PhonePe\\common\\configs\\Instruments' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/configs/Instruments.php',
        'PhonePe\\common\\configs\\MerchantConfig' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/configs/MerchantConfig.php',
        'PhonePe\\common\\configs\\Rails' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/configs/Rails.php',
        'PhonePe\\common\\eventHandler\\Event' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/eventHandler/Event.php',
        'PhonePe\\common\\eventHandler\\EventPublisher' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/eventHandler/EventPublisher.php',
        'PhonePe\\common\\exceptions\\PhonePeException' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/exceptions/PhonePeException.php',
        'PhonePe\\common\\tokenHandler\\OAuthToken' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/tokenHandler/OAuthToken.php',
        'PhonePe\\common\\tokenHandler\\TokenService' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/tokenHandler/TokenService.php',
        'PhonePe\\common\\utils\\CurlHttpClient' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/utils/CurlHttpClient.php',
        'PhonePe\\common\\utils\\HttpRequest' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/utils/HttpRequest.php',
        'PhonePe\\common\\utils\\HttpResponse' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/common/utils/HttpResponse.php',
        'PhonePe\\payments\\v2\\models\\request\\StandardCheckoutPayRequest' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/request/StandardCheckoutPayRequest.php',
        'PhonePe\\payments\\v2\\models\\request\\StandardCheckoutRefundRequest' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/request/StandardCheckoutRefundRequest.php',
        'PhonePe\\payments\\v2\\models\\request\\builders\\StandardCheckoutPayRequestBuilder' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/request/builders/StandardCheckoutPayRequestBuilder.php',
        'PhonePe\\payments\\v2\\models\\request\\builders\\StandardCheckoutRefundRequestBuilder' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/request/builders/StandardCheckoutRefundRequestBuilder.php',
        'PhonePe\\payments\\v2\\models\\response\\CallbackResponse' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/CallbackResponse.php',
        'PhonePe\\payments\\v2\\models\\response\\RefundStatusCheckResponse' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/RefundStatusCheckResponse.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\AccountInstrument' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/AccountInstrument.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\CreditCardInstrument' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/CreditCardInstrument.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\CreditLinePaymentInstrument' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/CreditLinePaymentInstrument.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\DebitCardInstrument' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/DebitCardInstrument.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\EgvPaymentInstrument' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/EgvPaymentInstrument.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\Instrument' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/Instrument.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\MetaInfo' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/MetaInfo.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\NetBankingInstrument' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/NetBankingInstrument.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\Payload' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/Payload.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\PaymentDetail' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/PaymentDetail.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\PgRail' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/PgRail.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\PpiEgvRail' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/PpiEgvRail.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\PpiWalletRail' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/PpiWalletRail.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\Rail' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/Rail.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\UpiRail' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/UpiRail.php',
        'PhonePe\\payments\\v2\\models\\response\\ResponseComponents\\WalletPaymentInstrument' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/ResponseComponents/WalletPaymentInstrument.php',
        'PhonePe\\payments\\v2\\models\\response\\StandardCheckoutPayResponse' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/StandardCheckoutPayResponse.php',
        'PhonePe\\payments\\v2\\models\\response\\StandardCheckoutRefundResponse' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/StandardCheckoutRefundResponse.php',
        'PhonePe\\payments\\v2\\models\\response\\StatusCheckResponse' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/models/response/StatusCheckResponse.php',
        'PhonePe\\payments\\v2\\standardCheckout\\StandardCheckoutClient' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/standardCheckout/StandardCheckoutClient.php',
        'PhonePe\\payments\\v2\\standardCheckout\\StandardCheckoutConstants' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/src/phonepe/sdk/pg/payments/v2/standardCheckout/StandardCheckoutConstants.php',
        'PhpToken' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
        'Stringable' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
        'UnhandledMatchError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
        'Unit\\EnvTest' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/tests/Unit/EnvTest.php',
        'Unit\\common\\TokenHandler\\TokenServiceTest' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/tests/Unit/common/TokenHandler/TokenServiceTest.php',
        'Unit\\common\\utils\\HttpRequestTest' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/tests/Unit/common/utils/HttpRequestTest.php',
        'Unit\\common\\utils\\RequestGeneratorTest' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/tests/Unit/common/utils/RequestGeneratorTest.php',
        'Unit\\payments\\v2\\standardCheckout\\StandardCheckoutClientTest' => __DIR__ . '/..' . '/phonepe/pg-php-sdk-v2/tests/Unit/payments/v2/standardCheckout/StandardCheckoutClientTest.php',
        'ValueError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitd74dfbc9727ce4532999700b6359c1e1::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitd74dfbc9727ce4532999700b6359c1e1::$prefixDirsPsr4;
            $loader->prefixesPsr0 = ComposerStaticInitd74dfbc9727ce4532999700b6359c1e1::$prefixesPsr0;
            $loader->classMap = ComposerStaticInitd74dfbc9727ce4532999700b6359c1e1::$classMap;

        }, null, ClassLoader::class);
    }
}
