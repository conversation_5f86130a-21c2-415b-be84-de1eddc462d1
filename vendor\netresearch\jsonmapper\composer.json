{"name": "netresearch/jsonmapper", "description": "Map nested JSON structures onto PHP classes", "license": "OSL-3.0", "autoload": {"psr-0": {"JsonMapper": "src/"}}, "autoload-dev": {"psr-4": {"namespacetest\\": "tests/support/namespacetest", "othernamespace\\": "tests/support/othernamespace", "Enums\\": "tests/support/Enums"}, "psr-0": {"JsonMapperTest_": "tests/support", "": ["tests/support/Zoo"]}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://github.com/cweiske/jsonmapper/", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/cweiske/jsonmapper/issues"}, "require": {"php": ">=7.1", "ext-spl": "*", "ext-json": "*", "ext-pcre": "*", "ext-reflection": "*"}, "require-dev": {"phpunit/phpunit": "~7.5 || ~8.0 || ~9.0 || ~10.0", "squizlabs/php_codesniffer": "~3.5"}}