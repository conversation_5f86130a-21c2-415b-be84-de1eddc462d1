<?php 
session_start();
include('../lib/db_connection.php');
dbQuery("DELETE FROM tabl_product_options WHERE id='".$_REQUEST['id']."'");

$sel_filter=dbQuery("SELECT * FROM tabl_product_options WHERE p_id='".$_REQUEST['p_id']."'");
 $array_color = Array();
 $array_size = Array();
 while($res_filter=dbFetchAssoc($sel_filter)){
	   if($res_filter['v_type']==1){
	   $array_color[] = $res_filter['v_id'];
	 }else{
		$array_size[] = $res_filter['v_id']; 
	 }
}
$color_filter=implode(",",$array_color);
$size_filter=implode(",",$array_size);

dbQuery("UPDATE tabl_product_filter SET color='".$color_filter."',size='".$size_filter."' WHERE p_id='".$_REQUEST['p_id']."'");

echo '1';
 
?>