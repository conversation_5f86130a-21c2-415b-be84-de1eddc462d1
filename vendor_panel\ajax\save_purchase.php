<?php 
session_start();
include('../lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d'); 

$purchase_date = date("Y-m-d",strtotime($_REQUEST['date_added']));

$insert=dbQuery("insert into tabl_purchase set buyer_id='".$_REQUEST['party']."',payment='".$_REQUEST['payment']."',ctn='".$_REQUEST['grand_total_ctn']."',total_pcs='".$_REQUEST['grand_total_pcs']."',total_price='".$_REQUEST['grand_total_amount']."',date_added='".$purchase_date."'");
$last=dbInsertId();
$invoice_no=$last;

dbQuery("insert into tabl_main_ledger set 
                    bill_id='".$invoice_no."',
					buyer_id='".$_REQUEST['party']."',
					entry_type='1',		
					purchase_amount='".$_REQUEST['grand_total_amount']."',
					description='Purchase',
					date_added='".$purchase_date."'");

$num=$_REQUEST['num']-1;

for($i=1;$i<=$num;$i++)
{
	if($_REQUEST['particular_'.$i]!=0)
	{
	 
	 $qry_detail=dbQuery("insert into tabl_purchase_details set 
										bill_id='".$invoice_no."',
										buyer_id='".$_REQUEST['party']."',
										particular_id='".$_REQUEST['particular_'.$i]."',										
										ctn='".$_REQUEST['ctn_'.$i]."',
										pcs='".$_REQUEST['pcs_'.$i]."',
										total_pcs='".$_REQUEST['total_pcs_'.$i]."',
										price='".$_REQUEST['price_'.$i]."',
										total_price='".$_REQUEST['total_amount_'.$i]."',
										payment='".$_REQUEST['payment']."',									
										date_added='".$purchase_date."'");								
                      $invoice_row_id=dbInsertId();
	
	
	
	dbQuery("INSERT INTO tabl_stock_ledger SET bill_id='".$invoice_no."',
												particular_id='".$_REQUEST['particular_'.$i]."',
												invoice_row_id='".$invoice_row_id."',
												buyer_id='".$_REQUEST['party']."',
												in_stock='".$_REQUEST['total_pcs_'.$i]."',
												type='1',
												date_added='".$purchase_date."'");										
										
										
										
	  $qry_check=dbQuery("select * from tabl_stock where particular_id='".$_REQUEST['particular_'.$i]."'");
	  $num_stock=dbNumRows($qry_check);
	  if($num_stock==0)
	  {
	  
	  $stock="insert into tabl_stock set particular_id='".$_REQUEST['particular_'.$i]."',in_stock='".$_REQUEST['total_pcs_'.$i]."'";
	 $qry_stock=dbQuery($stock);
	  }
	  else
	  {
 $stock="update tabl_stock set in_stock=in_stock+'".$_REQUEST['total_pcs_'.$i]."' where particular_id='".$_REQUEST['particular_'.$i]."'";	  
	$qry_stock=dbQuery($stock);
	     }	    
   }		
}
if($_REQUEST['payment']==1){
	
	dbQuery("insert into tabl_payment set 
                    bill_id='".$invoice_no."',
					buyer_id='".$_REQUEST['party']."',					
					amount='".$_REQUEST['grand_total_amount']."',
					description='Payment',
					date_added='".$purchase_date."'");
  $payment_no=dbInsertId();	
	
	
dbQuery("insert into tabl_main_ledger set 
                    bill_id='".$invoice_no."',
					payment_no='".$payment_no."',
					buyer_id='".$_REQUEST['party']."',					
					entry_type='3',
					sale_amount='".$_REQUEST['grand_total_amount']."',
					description='Payment',
					date_added='".$purchase_date."'");
  }
echo '1';
?>