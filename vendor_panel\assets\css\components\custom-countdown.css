/*
    Countdown Simple
*/

#cd-simple {
    display: flex;
    justify-content: space-around;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
#cd-simple .countdown { margin-bottom: 23px; }
#cd-simple .clock-count-container {
    width: 100px;
    height: 100px;
    -webkit-box-shadow: 1px 2px 12px 0 rgba(31,45,61,.1);
    box-shadow: 1px 2px 12px 0 rgba(31,45,61,.1);
    border-radius: 6px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    border: 1px solid #e0e6ed;
}
#cd-simple .clock-val {
    font-size: 35px;
    color: #1b55e2;
    margin-bottom: 0;
}
#cd-simple .clock-text {
    color: #3b3f5c;
    font-size: 15px;
    margin-bottom: 0;
    margin-top: 16px;
}


/*
    Countdown Circle
*/

#cd-circle {
    display: flex;
    justify-content: space-around;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
#cd-circle .countdown { margin-bottom: 23px; }
#cd-circle .clock-count-container {
    width: 85px;
    height: 85px;
    -webkit-box-shadow: 1px 2px 12px 0 rgba(31,45,61,.1);
    box-shadow: 1px 2px 12px 0 rgba(31,45,61,.1);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    border: 1px solid #e0e6ed;
    margin: 0 0 12px 0;
}
#cd-circle .clock-val {
    font-size: 25px;
    color: #1b55e2;
    margin-bottom: 0;
}
#cd-circle .clock-text {
    color: #3b3f5c;
    font-size: 15px;
    margin-bottom: 0;
    margin-top: 16px;
}