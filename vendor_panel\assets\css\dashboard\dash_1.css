.layout-spacing {
    padding-bottom: 25px;
}
.layout-top-spacing {
    margin-top: 15px;
}
.widget {
    position: relative;
    padding: 20px;
    border-radius: 8px;
    /*border: none;*/
    background: #fff;
}
.widget .widget-heading {
    margin-bottom: 15px;
}
.widget h5 {
    font-weight: 700;
    font-size: 19px;
    letter-spacing: 0px;
    margin-bottom: 0;
    color: #515365;
}
.widget .widget-content {}


/*
    ==================
        Total Sales
    ==================
*/

.widget-two {
    position: relative;
    background: #fff;
    padding: 0;
    border-radius: 8px;
    height: 100%;
        /*box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.09019607843137255), 0 1px 20px 0 rgba(0, 0, 0, 0.08), 0px 1px 11px 0px rgba(0, 0, 0, 0.06);*/
        border: 1px solid #e0e6ed;
    box-shadow: 0 0 40px 0 rgba(94,92,154,.06);
}
.widget-two .widget-content {
    font-size: 17px;
}
.widget-two .w-chart {
    position: absolute;
    bottom: 0;
    bottom: 0;
    right: 0;
    left: 0;
}
.widget-two .w-numeric-value {
    display: flex;
    color: #fff;
    font-weight: 500;
    padding: 20px;
    justify-content: space-between;
}
.widget-two .w-numeric-value .w-icon {
    display: inline-block;
    background: #ffeccb;
    padding: 13px 12px;
    border-radius: 50%;
    display: inline-flex;
    align-self: center;
    height: 45px;
    width: 45px;
}
.widget-two .w-numeric-value svg {
    display: block;
    color: #e2a03f;
    width: 20px;
    height: 20px;
}
.widget-two .w-numeric-value .w-value {
    font-size: 19px;
    display: block;
    color: #0e1726;
    font-weight: 700;
    margin-bottom: -9px;
}
.widget-two .w-numeric-value .w-numeric-title {
    font-size: 13px;
    color: #888ea8;
    font-weight: 600;
}

/*
    ==================
        Widget
    ==================
*/

.widget-one {
    position: relative;
    background: #1b55e2;
    padding: 0;
    border-radius: 8px;
    height: 100%;
}
.widget-one .widget-content {
    font-size: 17px;
}
.widget-one .w-numeric-value {
    position: absolute;
    display: flex;
    color: #fff;
    font-weight: 500;
    padding: 20px;
}
.widget-one .w-numeric-value .w-icon {
    display: inline-block;
    background: #fff;
    padding: 13px 12px;
    border-radius: 50%;
    display: inline-flex;
    align-self: center;
    height: 45px;
    width: 45px;
    margin-right: 14px;
}
.widget-one .w-numeric-value svg {
    display: block;
    color: #1b55e2;
    width: 20px;
    height: 20px;
}
.widget-one .w-numeric-value .w-value {
    font-size: 26px;
    display: block;
    color: #fff;
    font-weight: 600;
    margin-bottom: -9px;
}
.widget-one .w-numeric-value .w-numeric-title {
    font-size: 13px;
    color: #fff;
    letter-spacing: 1px;
    font-weight: 600;
}

/*
    ====================
        Order Summary
    ====================
*/

.widget-three {
    position: relative;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    height: 100%;
        /*box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.09019607843137255), 0 1px 20px 0 rgba(0, 0, 0, 0.08), 0px 1px 11px 0px rgba(0, 0, 0, 0.06);*/
        border: 1px solid #e0e6ed;
    box-shadow: 0 0 40px 0 rgba(94,92,154,.06);
}
.widget-three .widget-heading {
    margin-bottom: 54px;
}
.widget-three .widget-heading h5 {
    font-size: 19px;
    display: block;
    color: #0e1726;
    font-weight: 600;
    margin-bottom: 0;
}
.widget-three .widget-content {
    font-size: 17px;
}
.widget-three .widget-content .summary-list {
    display: flex;
}
.widget-three .widget-content .summary-list:not(:last-child) {
    margin-bottom: 30px;
}
.widget-three .widget-content .w-icon {
    display: inline-block;
    padding: 8px 8px;
    border-radius: 50%;
    display: inline-flex;
    align-self: center;
    height: 34px;
    width: 34px;
    margin-right: 12px;
}
.widget-three .widget-content .w-icon svg {
    display: block;
    width: 17px;
    height: 17px;
}
.widget-three .widget-content .summary-list:nth-child(1) .w-icon {
    background: #dccff7;
}
.widget-three .widget-content .summary-list:nth-child(2) .w-icon {
    background: #e6ffbf;
}
.widget-three .widget-content .summary-list:nth-child(3) .w-icon {
    background: #ffeccb ;
}
.widget-three .widget-content .summary-list:nth-child(1) .w-icon svg {
    color: #5c1ac3;
}
.widget-three .widget-content .summary-list:nth-child(2) .w-icon svg {
    color: #009688;
}
.widget-three .widget-content .summary-list:nth-child(3) .w-icon svg {
    color: #e2a03f;
}
.widget-three .widget-content .w-summary-details {
    width: 100%;
    align-self: center;
}
.widget-three .widget-content .w-summary-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1px;
}
.widget-three .widget-content .w-summary-info h6 {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 0;
    color: #888ea8;
}
.widget-three .widget-content .w-summary-info p {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 0;
    color: #888ea8;
}
.widget-three .widget-content .w-summary-stats .progress {
    margin-bottom: 0;
    height: 6px;
    border-radius: 20px;
    box-shadow: 0 2px 2px rgba(224, 230, 237, 0.4588235294117647), 1px 6px 7px rgba(224, 230, 237, 0.4588235294117647);
}

/*
    ==================
        Revenue
    ==================
*/
.widget-chart-one .widget-heading {
    display: flex;
    justify-content: space-between;
}
.widget-chart-one .apexcharts-legend-marker {
    left: -5px!important;
}
.widget-chart-one .apexcharts-yaxis-title {
    font-weight: 600;
    fill: #bfc9d4;
}
.widget-chart-one .apexcharts-xaxis-title {
    font-weight: 600;
    fill: #bfc9d4;
}
.widget-chart-one .widget-heading .tabs {
    padding: 0;
    margin: 0;
}
.widget-chart-one .widget-heading .tabs li {
    display: inline-block;
    list-style: none;
    padding: 0 0;
}
.widget-chart-one .widget-heading .tabs a {
    font-size: 14px;
    letter-spacing: 1px;
    font-weight: 600;
    padding: 5px 7px;
    background: #c2d5ff;
    color: #1b55e2;
    border-radius: 4px;
}

/*
    =======================
        Sold By cateory
    =======================
*/

.widget-chart-two {
    padding: 0;
}
.widget.widget-chart-two .widget-heading {
    padding: 20px 20px 0 20px;
}
.widget.widget-chart-two .widget-content {
    padding: 0 0 20px 0;
}
.widget-chart-two .apexcharts-canvas {
    margin: 0 auto;
}
.widget-chart-two .apexcharts-legend-marker {
    left: -5px!important;
}


/*
    ==================
        Transaction
    ==================
*/

.widget-table-one .widget-heading {
    display: flex;
}
.widget-table-one .widget-content {}
.widget-table-one .transactions-list {
    padding: 12px 12px;
    border: 1px dashed #bfc9d4;
    border-radius: 6px;
    -webkit-transition: all 0.1s ease;
    transition: all 0.1s ease;
}
.widget-table-one .transactions-list:not(:last-child) {
    margin-bottom: 15px;
}
.widget-table-one .transactions-list:hover {
    -webkit-transform: translateY(-1px) scale(1.01);
    transform: translateY(-1px) scale(1.01);
}
.widget-table-one .transactions-list .t-item {
    display: flex;
    justify-content: space-between;
}
.widget-table-one .transactions-list .t-item .t-company-name {
    display: flex;
}
.widget-table-one .transactions-list .t-item .t-icon {
    margin-right: 12px;
}
.widget-table-one .transactions-list .t-item .t-icon .avatar {
    position: relative;
    display: inline-block;
    width: 38px;
    height: 38px;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 1px;
}
.widget-table-one .transactions-list .t-item .t-icon .avatar .avatar-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #ffe1e2;
    color: #e7515a;
}
.widget-table-one .transactions-list .t-item .t-icon .icon {
    position: relative;
    display: inline-block;
    padding: 10px;
    background-color: #ffeccb;
    border-radius: 50%;
}
.widget-table-one .transactions-list .t-item .t-icon .icon svg {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 19px;
    height: 19px;
    color: #e2a03f;
    stroke-width: 2;
}
.widget-table-one .transactions-list .t-item .t-name {
    align-self: center;
}
.widget-table-one .transactions-list .t-item .t-name h4 {
    font-size: 15px;
    letter-spacing: 0px;
    font-weight: 600;
    margin-bottom: 0;
}
.widget-table-one .transactions-list .t-item .t-name .meta-date {
    font-size: 12px;
    margin-bottom: 0;
    font-weight: 600;
    color: #888ea8;
}
.widget-table-one .transactions-list .t-item .t-rate {
    align-self: center;
}
.widget-table-one .transactions-list .t-item .t-rate p {
    margin-bottom: 0;
    font-size: 13px;
    letter-spacing: 0px;
}
.widget-table-one .transactions-list .t-item .t-rate svg {
    width: 14px;
    height: 14px;
    vertical-align: baseline;
}
.widget-table-one .transactions-list .t-item .t-rate.rate-inc p {
    color: #009688;
}
.widget-table-one .transactions-list .t-item .t-rate.rate-inc svg {
    color: #009688;
}
.widget-table-one .transactions-list .t-item .t-rate.rate-dec p {
    color: #e7515a;
}
.widget-table-one .transactions-list .t-item .t-rate.rate-dec svg {
    color: #e7515a;
}


/*
    ========================
        Recent Activities
    ========================
*/
.widget-activity-four {
    padding-right: 0;
    padding-left: 0;
}
.widget-activity-four .mt-container {
    position: relative;
    height: 272px;
    overflow: auto;
    padding-right: 12px;
}
.widget-activity-four .widget-heading {
    padding: 0 20px;
}
.widget-activity-four .widget-content {
    padding: 0 8px 0 20px;
}
.widget-activity-four .timeline-line .item-timeline { display: flex; width: 100%; margin-bottom: 12px; }
.widget-activity-four .timeline-line .item-timeline .t-dot { position: relative; }
.widget-activity-four .timeline-line .item-timeline .t-dot:before {
    content: '';
    position: absolute;
    border-color: inherit;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    top: 5px;
    left: 5px;
    transform: translateX(-50%); 
    border-color: #e0e6ed;
    background: #bfc9d4;
    z-index: 1;
}
.widget-activity-four .timeline-line .item-timeline .t-dot:after {
    content: '';
    position: absolute;
    border-color: inherit;
    border-width: 1px;
    border-style: solid;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    left: 5px;
    transform: translateX(-50%);
    border-color: #e0e6ed;
    width: 0;
    height: auto;
    top: 12px;
    bottom: -19px;
    border-right-width: 0;
    border-top-width: 0;
    border-bottom-width: 0;
    border-radius: 0;
}
.widget-activity-four .timeline-line .item-timeline.timeline-primary .t-dot:before {
    background-color: #1b55e2;
    border-color: rgb(164, 189, 247);
}
.widget-activity-four .timeline-line .item-timeline.timeline-success .t-dot:before {
    background-color: #009688;
    border-color: rgb(154, 210, 205);

}
.widget-activity-four .timeline-line .item-timeline.timeline-danger .t-dot:before {
    background-color: #e7515a;
    border-color: rgb(241, 172, 176);
}
.widget-activity-four .timeline-line .item-timeline.timeline-dark .t-dot:before {
    background-color: #3b3f5c;
    border-color: rgb(159, 163, 187);
}
.widget-activity-four .timeline-line .item-timeline.timeline-secondary .t-dot:before {
    background: #1b55e2;
    border-color: #c2d5ff;
}
.widget-activity-four .timeline-line .item-timeline.timeline-warning .t-dot:before {
    background-color: #e2a03f;
    border-color: rgb(222, 199, 165);
}

.widget-activity-four .timeline-line .item-timeline:last-child .t-dot:after { display: none; }
.widget-activity-four .timeline-line .item-timeline .t-meta-time {
    margin: 0;
    min-width: 100px;
    max-width: 100px;
    font-size: 12px;
    font-weight: 700;
    color: #888ea8;
    align-self: center;
}
.widget-activity-four .timeline-line .item-timeline .t-text {
    align-self: center;
    margin-left: 20px;
    display: flex;
    width: 100%;
    justify-content: space-between;
}
.widget-activity-four .timeline-line .item-timeline .t-text p {
    font-size: 12px;
    margin: 0;
    color: #888ea8;
    font-weight: 400;
}
.widget-activity-four .timeline-line .item-timeline .t-text span.badge {
    position: absolute;
    right: 11px;
    padding: 2px 4px;
    font-size: 11px;
    letter-spacing: 1px;
    opacity: 0;
    font-weight: 400;
}
.widget-activity-four .timeline-line .item-timeline .t-text span.badge {
    transform: none;
}
.widget-activity-four .timeline-line .item-timeline:hover .t-text span.badge {
    opacity: 1;
}
.widget-activity-four .timeline-line .item-timeline .t-text p.t-time {
    text-align: right;
    color: #888ea8;
    font-size: 10px;
}
.widget-activity-four .timeline-line .item-timeline .t-time {
    margin: 0;
    min-width: 80px;
    max-width: 80px;
    font-size: 13px;
    font-weight: 600; 
    color: #acb0c3;
    letter-spacing: 1px;
}
.widget-activity-four .tm-action-btn {
    text-align: center;
    padding-top: 20px;
}
.widget-activity-four .tm-action-btn button {
    background: transparent;
    box-shadow: none;
    padding: 0;
    color: #1b55e2;
    font-weight: 600;
    letter-spacing: 0.5px;
    border: none;
    font-size: 15px;
}
.widget-activity-four .tm-action-btn svg {
    width: 15px;
    height: 15px;
    vertical-align: inherit;
}

/*
    =====================
        Account Info
    =====================
*/

.widget-account-invoice-one .invoice-box {}
.widget-account-invoice-one .invoice-box .acc-total-info {
    padding: 0 0;
    margin-bottom: 18px;
    padding-bottom: 18px;
    border-bottom: 1px dashed #bfc9d4;
}
.widget-account-invoice-one .invoice-box h5 {
    text-align: center;
    font-size: 20px;
    letter-spacing: 1px;
    margin-bottom: 10px;
    color: #1b55e2;
}
.widget-account-invoice-one .invoice-box .acc-amount {
    text-align: center;
    font-size: 23px;
    font-weight: 700;
    margin-bottom: 0;
    color: #009688;
}
.widget-account-invoice-one .invoice-box .inv-detail {
    margin-bottom: 18px;
    padding-bottom: 18px;
    border-bottom: 1px dashed #bfc9d4;
}
.widget-account-invoice-one .invoice-box [class*="info-detail-"]:not(.info-sub) {
    display: flex;
    justify-content: space-between;
}
.widget-account-invoice-one .invoice-box [class*="info-detail-"]:not(.info-sub) p {
    margin-bottom: 13px;
    font-weight: 700;
    font-size: 14px;
}
.widget-account-invoice-one .invoice-box [class*="info-detail-"].info-sub .info-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0;
    font-weight: 700;
    font-size: 14px;
}
.widget-account-invoice-one .invoice-box [class*="info-detail-"].info-sub .info-detail p {
    margin-bottom: 0;
}
.widget-account-invoice-one .invoice-box [class*="info-detail-"].info-sub .info-detail-sub {
    margin-left: 9px;
}
.widget-account-invoice-one .invoice-box [class*="info-detail-"].info-sub .info-detail-sub p {
    color: #888ea8;
    margin-bottom: 2px;
    font-weight: 600;
}
.widget-account-invoice-one .invoice-box .inv-action {
    text-align: center;
    display: flex;
    justify-content: space-around;
}
.widget-account-invoice-one .invoice-box .inv-action a {
    transform: none;
}

/*
    =====================
        Recent Orders
    =====================
*/
.widget-table-two {
    position: relative;
}
.widget-table-two h5 {
    font-weight: 700;
    font-size: 19px;
    letter-spacing: 1px;
    margin-bottom: 20px;
}
.widget-table-two .widget-content {
    background: transparent;
}
.widget-table-two .table {
    border-collapse: separate;
    border-spacing: 0 5px;
    margin-bottom: 0;
}
.widget-table-two .table > thead > tr > th {
    text-transform: initial;
    font-weight: 600;
    border-top: none;
    background: #e0e6ed;
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 0;
    padding-left: 0;
    -webkit-transition: all 0.1s ease;
    transition: all 0.1s ease;
    padding: 10px 0 10px 5px;
}
.widget-table-two .table > thead > tr > th:first-child {
    border-bottom-left-radius: 6px;
}
.widget-table-two .table > thead > tr > th:first-child {
    border-top-left-radius: 6px;
}
.widget-table-two .table > thead > tr > th:last-child {
    border-bottom-right-radius: 6px;
}
.widget-table-two .table > thead > tr > th:last-child {
    border-top-right-radius: 6px;
}
.widget-table-two .table > thead > tr > th .th-content {
    color: #1b55e2;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 1px;
}
.widget-table-two .table > thead > tr > th:first-child .th-content {
    margin-left: 10px;
}
.widget-table-two .table > thead > tr > th:last-child .th-content {
    text-align: right;
    margin-right: 10px;
}
.widget-table-two .table > thead > tr > th:nth-last-child(2) .th-content {
    text-align: center;
    padding: 0 15px 0 0;
}
.widget-table-two .table > tbody > tr > td {
    border-top: none;
    background: transparent;
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 0;
    padding-left: 0;
    -webkit-transition: all 0.1s ease;
    transition: all 0.1s ease;
}
.widget-table-two .table > tbody > tr:hover > td {
    transform: translateY(-1px) scale(1.01);
}
.widget-table-two .table > tbody > tr > td .td-content {
    cursor: pointer;
    font-weight: 600;
    letter-spacing: 1px;
    color: #888ea8;
}
.widget-table-two .table > tbody > tr:hover > td .td-content {
    color: #515365;
}
.widget-table-two .table > tbody > tr > td:first-child {
    border-top-left-radius: 6px;
    padding: 10px 0 10px 15px;
}
.widget-table-two .table > tbody > tr > td:first-child {
    border-bottom-left-radius: 6px;
}
.widget-table-two .table > tbody > tr > td:last-child {
    border-top-right-radius: 6px;
    padding: 15.5px 15px 15.5px 0;
    text-align: right;
}
.widget-table-two .table > tbody > tr > td:last-child {
    border-bottom-right-radius: 6px;
}
.widget-table-two .table .td-content.customer-name {
    color: #515365;
}
.widget-table-two .table .td-content.product-brand {
    letter-spacing: 1px;
}
.widget-table-two .table .td-content img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    margin-right: 13px;
}
.widget-table-two .table tr > td:nth-last-child(2) .td-content {
    text-align: center;
}
.widget-table-two .table .td-content .badge {
    transform: none;
}
.widget-table-two .table tr:hover .td-content .badge {
    transform: translateY(-3px);
}
.widget-table-two .table tr .td-content .outline-badge-primary {
    background-color: #c2d5ff;
}
.widget-table-two .table tr .td-content .outline-badge-success {
    color: #009688;
    background-color: #e6ffbf;
    border-color: #009688;
}
.widget-table-two .table tr .td-content .outline-badge-danger {
    background-color: #ffe1e2;
}


/*
    ===========================
        Top Selling Product
    ===========================
*/

.widget-table-three {
    position: relative;
}
.widget-table-three h5 {
    font-weight: 700;
    font-size: 19px;
    letter-spacing: 1px;
    margin-bottom: 20px;
}
.widget-table-three .widget-content {
    background: transparent;
}
.widget-table-three .table {
    border-collapse: separate;
    border-spacing: 0 5px;
    margin-bottom: 0;
}
.widget-table-three .table > thead > tr > th:first-child .th-content {
    margin-left: 10px;
}
.widget-table-three .table > thead > tr > th:last-child .th-content {
    text-align: right; 
    margin-right: 10px;
}
.widget-table-three .table > thead > tr > th {
    text-transform: initial;
    font-weight: 600;
    border-top: none;
    background: #e0e6ed;
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 0;
    padding-left: 0;
    -webkit-transition: all 0.1s ease;
    transition: all 0.1s ease;
    padding: 10px 0 10px 15px;
}
.widget-table-three .table > thead > tr > th {
}
.widget-table-three .table > thead > tr > th:first-child {
    border-bottom-left-radius: 6px;
}
.widget-table-three .table > thead > tr > th:first-child {
    border-top-left-radius: 6px;
}
.widget-table-three .table > thead > tr > th:last-child {
    border-bottom-right-radius: 6px;
}
.widget-table-three .table > thead > tr > th:last-child {
    border-top-right-radius: 6px;
}
.widget-table-three .table > thead > tr > th .th-content {
    color: #1b55e2;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 1px;
}
.widget-table-three .table > thead > tr > th:nth-last-child(2) .th-content {}
.widget-table-three .table > tbody > tr > td {
    border-top: none;
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 0;
    padding-left: 0;
    -webkit-transition: all 0.1s ease;
    transition: all 0.1s ease;
}
.widget-table-three .table > tbody > tr:hover > td {
    transform: translateY(-1px) scale(1.01);
}
.widget-table-three .table > tbody > tr {
    background: transparent;
}
.widget-table-three .table > tbody > tr > td .td-content {
    cursor: pointer;
    font-weight: 600;
    letter-spacing: 1px;
    color: #888ea8;
}
.widget-table-three .table > tbody > tr:hover > td .td-content {
    color: #3b3f5c;
}
.widget-table-three .table > tbody > tr > td:first-child {
    border-top-left-radius: 6px;
    padding: 10px 0px 10px 15px;
}
.widget-table-three .table > tbody > tr > td:first-child {
    border-bottom-left-radius: 6px;
}
.widget-table-three .table > tbody > tr > td:last-child {
    border-top-right-radius: 6px;
}
.widget-table-three .table > tbody > tr > td:last-child {
    border-bottom-right-radius: 6px;
}
.widget-table-three .table > tbody > tr > td:last-child .td-content {
    text-align: right;
    padding: 0 15px 0 0;
}
.widget-table-three .table tr > td:nth-last-child(2) .td-content {
    padding: 10px 0 10px 15px;
}
.widget-table-three .table .td-content .discount-pricing {
    padding: 10px 0 10px 15px;
}
.widget-table-three .table .td-content.product-name {
    color: #515365;
    letter-spacing: 1px;
}
.widget-table-three .table .td-content img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    margin-right: 10px;
}
.widget-table-three .table .td-content .pricing {
    padding: 10px 0 10px 15px;
}
.widget-table-three .table .td-content .tag {
    background: transparent;
    transform: none;
    font-weight: 600;
    letter-spacing: 2px;
    padding: 2px 5px;
    border-radius: 6px;
}
.widget-table-three .table .td-content .tag-primary {
    color: #1b55e2;
    border: 1px dashed #1b55e2;
    background: #c2d5ff;
}
.widget-table-three .table .td-content .tag-success {
    color: #009688;
    border: 1px dashed #009688;
    background: #e6ffbf;
}
.widget-table-three .table .td-content .tag-danger {
    color: #e7515a;
    border: 1px dashed #e7515a;
    background: #ffe1e2;
}
.widget-table-three .table .td-content a {
    padding: 0;
    font-size: 13px;
    background: transparent;
    transform: none;
    letter-spacing: 1px;
    border-bottom: 1px dashed #bfc9d4;
}

/*
    ====================
        Media Object
    ====================
*/

@media(min-width: 1200px) {
    .table-responsive {
        overflow-x: hidden;
    }
}
@media(max-width: 1430px) and (min-width: 1200px) {
    /*
        ===========================
            Top Selling Product
        ===========================
    */
    .widget-table-two .table .td-content img {
        display: block;
    }

    /*
        ===========================
            Top Selling Product
        ===========================
    */
    .widget-table-three .table .td-content img {
        display: block;
    }
}

@media(max-width: 767px) {
    .widget-notification-two button {
        display: none;
    }
}
@media(max-width: 575px) {

    /*
        ==================
            Total Sales
        ==================
    */

    .widget-two .w-chart {
        position: inherit;
    }

    /*
        ========================
            Recent Activities
        ========================
    */

    .widget-activity-one .mt-container {
        height: auto;
    }

    /*
        ===========================
            Top Selling Product
        ===========================
    */
    .widget-table-two .table > thead > tr > th {
        padding-right: 15px;
    }
    .widget-table-two .table > tbody > tr > td {
        border-top: none;
        padding-top: 0;
        padding-bottom: 0;
        padding-right: 12px;
        padding-left: 12px;
    }
    .widget-table-two .table .td-content.customer-name {
        color: #515365;
        text-align: center;
    }
    .widget-table-two .table .td-content.product-brand {
        text-align: center;
    }
    .widget-table-two .table .td-content img {
        display: block;
        margin: 0 auto 5px auto;
    }

    /*
        ===========================
            Top Selling Product
        ===========================
    */
    .widget-table-three .table > thead > tr > th {
        padding-right: 15px;
    }
    .widget-table-three .table > tbody > tr > td {
        border-top: none;
        padding-top: 0;
        padding-bottom: 0;
        padding-right: 12px;
        padding-left: 12px;
    }
    .widget-table-three .table .td-content.product-name {
        text-align: center;
    }
    .widget-table-three .table .td-content img {
        display: block;
        margin: 0 auto 5px auto;
    }

}