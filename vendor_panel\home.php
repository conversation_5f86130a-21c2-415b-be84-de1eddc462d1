<?php 


session_start();


include('lib/db_connection.php');


include('lib/get_functions.php');


include('lib/auth.php');


include('inc/resize-class.php');


$page=1;


$sub_page=0;


date_default_timezone_set("Asia/Kolkata");


$date=date('Y-m-d');


?>


<!DOCTYPE html>


<html lang="en">


<head>


    <meta charset="utf-8">


    <meta http-equiv="X-UA-Compatible" content="IE=edge">


    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">


    <title><?php echo SITE; ?> - DashBoard </title>


    <link rel="icon" type="image/x-icon" href="assets/img/favicon.ico"/>


    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />


    <script src="assets/js/loader.js"></script>





    <!-- BEGIN GLOBAL MANDATORY STYLES -->


    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">


    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />


    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />


    <!-- END GLOBAL MANDATORY STYLES -->





    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->


    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">


    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />


    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->





</head>


<body class="alt-menu sidebar-noneoverflow">





  <?php include('inc/__header.php');?>


    <!--  BEGIN MAIN CONTAINER  -->


    <div class="main-container" id="container">





        <div class="overlay"></div>


        <div class="search-overlay"></div>





        <!--  BEGIN TOPBAR  -->


        <?php include('inc/__menu.php');?>


        <!--  END TOPBAR  -->


        


        <!--  BEGIN CONTENT PART  -->


        <div id="content" class="main-content">


            <div class="layout-px-spacing">


                <div class="row layout-top-spacing">


                    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 layout-spacing">
          <div class="widget-four">
         
            <div class="widget-heading">
              <h5 class="">Return Orders</h5>
              <div class="widget-content">
                                <div class="table-responsive">
                                    <table id="zero-config" class="table table-hover" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
											<th>Date</th>
											<th>OrderID</th>
                                            <th>Name</th>
											<th>Email</th>
											<th>Phone</th>
											<th>Total Amount</th>
											<th>Status</th>
											<th>View</th>
                                           
                                        </tr>
                                    </thead>
                                    <tbody>
                                       <?php 
					 $sel_order=dbQuery("SELECT sum(total)as total_price,order_id FROM tabl_order_product WHERE vendor_id='".$_SESSION['vendor_id']."' GROUP BY order_id ORDER BY order_id");
										$i=1;
										while($res_order=dbFetchAssoc($sel_order)){
											$sel=dbQuery("SELECT * FROM tabl_order where id='".$res_order['order_id']."'");
											$res=dbFetchAssoc($sel);?>
                                        <tr>
                                              <td><?php echo $i;?></td>
											  <td><?php echo $res['date_added'];?></td>
                                             <td>Maddamall-<?php echo $res['id'];?></td>
                                             <td><?php echo $res['name'];?></td> 									 
											 <td><?php echo $res['email'];?></td>
											 <td><?php echo $res['phone'];?></td>
                                             <td><?php echo $res['total'];?></td>
											 <td><?php if($res['order_status_id']==1){?>
											   <div class="badge badge-success">Complete</div>
											 <?php }elseif($res['order_status_id']==2){?>
												 <div class="badge badge-warning">Processed</div>
											 <?php }elseif($res['order_status_id']==3){?>
												 <div class="badge badge-warning">Cancel</div>
											 <?php }elseif($res['order_status_id']==4){?>
												 <div class="badge badge-info">Delivered</div>
											 <?php }else{
												   if($res['return_request']==0){
													   echo '<div class="badge badge-danger">Return</div>';
													   }elseif($res['return_request']==1){
														    echo '<div class="badge badge-danger" onClick="return_order_success('.$res['id'].','.$i.')">Requested Return</div>';  
														   }else{
															echo '<div class="badge badge-danger">Return Success</div>';     
															   }?>
											 <?php } ?>
                                             <div class="return_order_<?php echo $i;?>"></div>
											 </td>
											 <td><a href="order_view.php?order_id=<?php echo $res['id'];?>"><div class="badge badge-primary">View</div></a></td>
                                                                                      
                                        </tr>
                                        <?php $i++; } ?>
                                    </tbody>                                    
                                </table>
                                </div>
                            </div>
            </div>
          </div>
        </div>


                </div>





                <?php include('inc/__footer.php');?>





            </div>


        </div>


        <!--  END CONTENT PART  -->





    </div>


    <!-- END MAIN CONTAINER -->





    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->


    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>


    <script src="bootstrap/js/popper.min.js"></script>


    <script src="bootstrap/js/bootstrap.min.js"></script>


    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>


    <script src="assets/js/app.js"></script>


    <script>


        $(document).ready(function() {


            App.init();


        });


    </script>


    <script src="assets/js/custom.js"></script>


    <!-- END GLOBAL MANDATORY SCRIPTS -->





    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->


    <script src="plugins/apex/apexcharts.min.js"></script>


    <script src="assets/js/dashboard/dash_2.js"></script>


    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->


</body>
</html>
<script>
function return_order_success(order_id,row_id){
var retVal = confirm("Are you sure want to Return this order.");
	if( retVal == true ){
  $(".return_order_"+row_id).html('<img src="../loader.gif" style="width: 10px;"></i> please wait...');
 $.ajax({
	  url:'ajax/return_order_success.php',
	  type:'post',
	  data: {'order_id':order_id},
	  success:function(data){
	if(data==1)
		  {     
	      	location.reload();		
		  }
		},
	  });
	}else{
        return false;
   }
}
</script>
