<div class="topbar-nav header navbar" role="banner">
            <nav id="topbar">
                <ul class="navbar-nav theme-brand flex-row  text-center">
                    <li class="nav-item theme-text">
                        <a href="home.php" class="nav-link"> <?php echo SITE;?> </a>
                    </li>
                </ul>

                <ul class="list-unstyled menu-categories" id="topAccordion">

                    <li class="menu single-menu <?php if($page==1){echo 'active';}else{echo '';} ?>">
                        <a href="home.php">
                            <div class="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-home"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
                                <span>Home</span>
                            </div>

                        </a>                        
                    </li>
         <!--   <li class="menu single-menu <?php if($page==2){echo 'active';}else{echo '';} ?>">
                        <a href="#app" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                            <div class="">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Catalog</span>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down"><polyline points="6 9 12 15 18 9"></polyline></svg>
                        </a>
                        <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                            <li class="<?php if($sub_page==21){echo 'active';}else{echo '';} ?>">
                                <a href="main_category.php"> Main Category </a>
                            </li>
                             <li class="<?php if($sub_page==22){echo 'active';}else{echo '';} ?>">
                                <a href="category.php"> Category </a>
                            </li>
							
							<li class="<?php if($sub_page==23){echo 'active';}else{echo '';} ?>">
                                <a href="sub_category.php"> Sub-Category </a>
                            </li>
                            <!--
                             <li class="<?php if($sub_page==24){echo 'active';}else{echo '';} ?>">
                                <a href="products.php"> Products </a>
                            </li>
                    
                        </ul>
                    </li>-->
					
				<li class="menu single-menu <?php if($page==3){echo 'active';}else{echo '';} ?>">
                        <a href="my-account.php">
                            <div class="">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Profile</span>
                            </div>

                        </a>                        
                    </li>
					<li class="menu single-menu <?php if($page==4){echo 'active';}else{echo '';} ?>">
                        <a href="products.php">
                            <div class="">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Products</span>
                            </div>

                        </a>                        
                    </li>
					<li class="menu single-menu <?php if($page==7){echo 'active';}else{echo '';} ?>">
                        <a href="orders.php">
                            <div class="">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Orders</span>
                            </div>

                        </a>                        
                    </li>
					
					<!--<li class="menu single-menu <?php if($page==4){echo 'active';}else{echo '';} ?>">
                        <a href="live_class.php">
                            <div class="">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Live Class</span>
                            </div>

                        </a>                        
                    </li>
					
					<li class="menu single-menu <?php if($page==5){echo 'active';}else{echo '';} ?>">
                        <a href="blogs.php">
                            <div class="">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Blogs</span>
                            </div>

                        </a>                        
                    </li>
					
                    
					
					<li class="menu single-menu <?php if($page==6){echo 'active';}else{echo '';} ?>">
                        <a href="#app" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                            <div class="">
                               <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Shop</span>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down"><polyline points="6 9 12 15 18 9"></polyline></svg>
                        </a>
                        <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                            <li class="<?php if($sub_page==61){echo 'active';}else{echo '';} ?>">
                                <a href="shop_category.php"> Shop Category </a>
                            </li> 
							<li class="<?php if($sub_page==62){echo 'active';}else{echo '';} ?>">
                                <a href="products.php"> Products </a>
                            </li> 
                        
                        </ul>
                    </li>
                    
                   <li class="menu single-menu <?php if($page==7){echo 'active';}else{echo '';} ?>">
                        <a href="orders.php">
                            <div class="">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Orders</span>
                            </div>

                        </a>                        
                    </li>
                   <!--  <li class="menu single-menu <?php if($page==3){echo 'active';}else{echo '';} ?>">
                        <a href="home.php">
                            <div class="">
                               <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Instructor</span>
                            </div>

                        </a>                        
                    </li>-->
                                        
                </ul>
            </nav>
        </div>