.custom-autocomplete {
    height: 328px;
}
.autocomplete-suggestions { 
    -webkit-box-sizing: border-box; 
    -moz-box-sizing: border-box; 
    box-sizing: border-box; 
    border: 1px solid #e0e6ed; 
    background: #FFF; 
    cursor: default; 
    overflow: auto; 
    z-index: 888!important;
    -webkit-box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
    -moz-box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
    box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
    max-height: 228px!important;
}
.autocomplete-suggestion { 
    padding: 5px 20px; 
    white-space: nowrap; 
    overflow: hidden; 
    color: #888ea8;
    letter-spacing: 1px; 
}
.autocomplete-no-suggestion { 
    padding: 5px 19px;
}
.autocomplete-selected { 
    background: #f1f2f1;
}
.autocomplete-suggestions strong { 
    font-weight: bold; 
    color: #1b55e2; 
}
.autocomplete-group { 
    padding: 12px 19px;
    font-weight: bold;
    font-size: 16px;
    color: #3b3f5c;
    display: block;
    border-bottom: 1px solid #e0e6ed;
    margin-bottom: 19px;
    border-top: 1px solid #e0e6ed;
}
.autocomplete-group:not(:first-child) {
    margin-top: 19px;
}

@media(max-width: 991px) {
    .custom-autocomplete {
        height: 450px;
    }
}