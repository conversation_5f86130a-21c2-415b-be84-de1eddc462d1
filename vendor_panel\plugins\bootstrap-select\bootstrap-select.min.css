select.bs-select-hidden,
select.selectpicker {
    display: none!important
}
.bootstrap-select {
    width: 220px \0
}
.bootstrap-select.btn-group>.dropdown-toggle {
    height: auto;
    border: 1px solid #bfc9d4;
    color: #3b3f5c!important;
    font-size: 15px;
    padding: 8px 10px;
    letter-spacing: 1px;
    background-color: #fff; 
    height: auto;
    padding: .75rem 1.25rem;
    border-radius: 6px;
    box-shadow: none;
}
.bootstrap-select>.dropdown-toggle {
    width: 100%;
    padding-right: 25px;
    z-index: 1
}
.bootstrap-select>.dropdown-toggle.bs-placeholder,
.bootstrap-select>.dropdown-toggle.bs-placeholder:hover,
.bootstrap-select>.dropdown-toggle.bs-placeholder:focus,
.bootstrap-select>.dropdown-toggle.bs-placeholder:active {
    color: #888ea8
}
.bootstrap-select>select {
    position: absolute!important;
    bottom: 0;
    left: 50%;
    display: block!important;
    width: .5px!important;
    height: 100%!important;
    padding: 0!important;
    opacity: 0!important;
    border: 0
}
.bootstrap-select>select.mobile-device {
    top: 0;
    left: 0;
    display: block!important;
    width: 100%!important;
    z-index: 2
}
.has-error .bootstrap-select .dropdown-toggle,
.error .bootstrap-select .dropdown-toggle {
    border-color: #b94a48
}
.bootstrap-select.fit-width {
    width: auto!important
}
.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
    width: 220px
}
.bootstrap-select.show .dropdown-toggle { border: 1px solid #1b55e2!important;
    box-shadow: 0 0 5px 2px rgba(194, 213, 255, 0.6196078431372549);
    color: #3b3f5c;
}
.bootstrap-select.show .dropdown-toggle.btn-outline-primary { border: 1px solid #1b55e2 !important; }
.bootstrap-select.show .dropdown-toggle.btn-outline-info { border: 1px solid #2196f3 !important; }
.bootstrap-select.show .dropdown-toggle.btn-outline-success { border: 1px solid #8dbf42 !important; }
.bootstrap-select.show .dropdown-toggle.btn-outline-warning { border: 1px solid #e2a03f !important; }
.bootstrap-select.show .dropdown-toggle.btn-outline-danger { border: 1px solid #e7515a !important; }
.bootstrap-select.form-control {
    margin-bottom: 0;
    padding: 0;
    border: 0;
    background: transparent; 
}
.bootstrap-select.form-control:not([class*=col-]) {
    width: 100%
}
.bootstrap-select.form-control.input-group-btn {
    z-index: auto
}
.bootstrap-select.form-control.input-group-btn:not(:first-child):not(:last-child)>.btn {
    border-radius: 0
}
.bootstrap-select.btn-group:not(.input-group-btn),
.bootstrap-select.btn-group[class*=col-] {
    float: none;
    display: inline-block;
    margin-left: 0
}
.bootstrap-select.btn-group.dropdown-menu-right,
.bootstrap-select.btn-group[class*=col-].dropdown-menu-right,
.row .bootstrap-select.btn-group[class*=col-].dropdown-menu-right {
    float: right
}
.form-inline .bootstrap-select.btn-group,
.form-horizontal .bootstrap-select.btn-group,
.form-group .bootstrap-select.btn-group {
    margin-bottom: 0
}

.form-group-lg .bootstrap-select.btn-group.form-control,
.form-group-sm .bootstrap-select.btn-group.form-control {
    padding: 0
}

.form-group-lg .bootstrap-select.btn-group.form-control .dropdown-toggle,
.form-group-sm .bootstrap-select.btn-group.form-control .dropdown-toggle {
    height: 100%;
    font-size: inherit;
    line-height: inherit;
    border-radius: inherit
}
.form-inline .bootstrap-select.btn-group .form-control {
    width: 100%
}
.bootstrap-select.btn-group.disabled,
.bootstrap-select.btn-group>.disabled {
    cursor: not-allowed
}
.bootstrap-select.btn-group.disabled:focus,
.bootstrap-select.btn-group>.disabled:focus {
    outline: 0!important
}
.bootstrap-select.btn-group.bs-container {
    position: absolute;
    height: 0!important;
    padding: 0!important
}
.bootstrap-select.btn-group.bs-container .dropdown-menu {
    z-index: 1060
}
.bootstrap-select.btn-group .dropdown-toggle .filter-option {
    display: inline-block;
    text-align: left
}
.bootstrap-select.btn-group .dropdown-toggle .caret {
    position: absolute;
    top: 50%;
    right: 12px;
    margin-top: -2px;
    vertical-align: middle
}
.bootstrap-select.btn-group .dropdown-toggle:after {
	display: none;
}
.caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    vertical-align: middle;
    border-top: 4px dashed;
    border-top: 4px solid\9;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
}
.bootstrap-select.btn-group[class*=col-] .dropdown-toggle {
    width: 100%
}
.bootstrap-select.btn-group .dropdown-menu {
    min-width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0;
    -webkit-box-shadow: 2px 5px 17px 0 rgba(31,45,61,.1);
    box-shadow: 2px 5px 17px 0 rgba(31,45,61,.1);
}
.bootstrap-select.btn-group .dropdown-menu .popover-title {
	padding: 15px;
}
.bootstrap-select.btn-group .dropdown-menu.inner {
    display: block;
    position: static;
    float: none;
    border: 0;
    padding: 0;
    margin: 0;
    border-radius: 0;
    box-shadow: none
}
.bootstrap-select.btn-group .dropdown-menu a.dropdown-item {
    position: relative;
    cursor: pointer;
    user-select: none;
    padding: 0;
}
.bootstrap-select.btn-group .dropdown-menu a.dropdown-item.active small {
    color: #fff
}
.bootstrap-select.btn-group .dropdown-menu a.dropdown-item.disabled a {
    cursor: not-allowed
}
.bootstrap-select.btn-group .dropdown-menu a.dropdown-item.hidden {
    display: none
}
.bootstrap-select .dropdown-header {
    padding: 10px 22px;
}
.bootstrap-select .dropdown-item.active, .bootstrap-select .dropdown-item:active {
    background-color: transparent;
    color: #515365;
}
.bootstrap-select.btn-group .dropdown-menu a.dropdown-item span.dropdown-item-inner {
    display: block;
    padding: 9px 19px;
    font-weight: 600;
}
.bootstrap-select.btn-group .dropdown-menu a.dropdown-item span.dropdown-item-inner:not([class*="bg-"]):hover {
    color: #1b55e2;
    background-color: rgba(27, 85, 226, 0.23921568627450981);
}
.bootstrap-select.btn-group .dropdown-menu a.dropdown-item span.dropdown-item-inner.opt {
    position: relative;
    padding-left: 2.25em
}
.bootstrap-select.btn-group .dropdown-menu a.dropdown-item span.dropdown-item-inner span.check-mark {
    display: none
}
.bootstrap-select.btn-group .dropdown-menu a.dropdown-item span.dropdown-item-inner span.text {
    display: inline-block
}
.bootstrap-select.btn-group .dropdown-menu a.dropdown-item small {
    padding-left: .5em
}
.bootstrap-select.btn-group .dropdown-menu .dropdown-item .span.check-mark {
    display: none
}
.bootstrap-select.btn-group .dropdown-menu .dropdown-item .span.text {
    display: inline-block
}
.bootstrap-select.btn-group .dropdown-menu .notify {
    position: absolute;
    bottom: 5px;
    width: 96%;
    margin: 0 2%;
    min-height: 26px;
    padding: 3px 5px;
    background: #f1f2f3;
    border: 1px solid #e3e3e3;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
    pointer-events: none;
    opacity: .9;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}
.bootstrap-select.btn-group .no-results {
    padding: 3px;
    background: #f1f2f3;
    margin: 0 5px;
    white-space: nowrap
}
.bootstrap-select.btn-group.fit-width .dropdown-toggle .filter-option {
    position: static
}
.bootstrap-select.btn-group.fit-width .dropdown-toggle .caret {
    position: static;
    top: auto;
    margin-top: -1px
}
.bootstrap-select.btn-group.show-tick .dropdown-menu a.selected span.dropdown-item-inner span.check-mark {
    position: absolute;
    display: inline-block;
    right: 15px;
    margin-top: 5px
}
.bootstrap-select.btn-group.show-tick .dropdown-menu a a span.text { margin-right: 34px; }
.bootstrap-select.show-menu-arrow.open>.dropdown-toggle { z-index: 1061 }
.bootstrap-select.show-menu-arrow .dropdown-toggle:before {
    content: '';
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 7px solid rgba(204, 204, 204, .2);
    position: absolute;
    bottom: -4px;
    left: 9px;
    display: none
}
.bootstrap-select.show-menu-arrow .dropdown-toggle:after {
    content: '';
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #fff;
    position: absolute;
    bottom: -4px;
    left: 10px;
    display: none
}
.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:before {
    bottom: auto;
    top: -3px;
    border-top: 7px solid rgba(204, 204, 204, .2);
    border-bottom: 0
}
.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:after {
    bottom: auto;
    top: -3px;
    border-top: 6px solid #fff;
    border-bottom: 0
}
.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle:before {
    right: 12px;
    left: auto
}
.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle:after {
    right: 13px;
    left: auto
}
.bootstrap-select.show-menu-arrow.open>.dropdown-toggle:before,
.bootstrap-select.show-menu-arrow.open>.dropdown-toggle:after { display: block }
.bs-searchbox,
.bs-actionsbox,
.bs-donebutton { padding: 4px 8px }
.bs-actionsbox {
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}
.bs-actionsbox .btn-group button { width: 50%; }
.bs-donebutton {
    float: left;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}
.bs-donebutton .btn-group button { width: 100%; }
.bs-searchbox+.bs-actionsbox { padding: 0 8px 4px }
.bs-searchbox .form-control {
    margin-bottom: 0;
    width: 100%;
    float: none
}
.input-group .bs-searchbox .form-control { width: 100% }
.btn-outline-primary:hover {
    color: #1b55e2 !important;
}
.btn-outline-info:hover {
    color: #2196f3 !important;
}
.btn-outline-warning:hover {
    color: #e2a03f !important;
}
.btn-outline-success:hover {
    color: #8dbf42 !important;
}
.btn-outline-danger:hover {
    color: #e7515a !important;
}

.bootstrap-select.btn-group>.dropdown-toggle.btn-outline-primary,
.bootstrap-select.btn-group>.dropdown-toggle.btn-outline-success,
.bootstrap-select.btn-group>.dropdown-toggle.btn-outline-info,
.bootstrap-select.btn-group>.dropdown-toggle.btn-outline-danger,
.bootstrap-select.btn-group>.dropdown-toggle.btn-outline-warning {
    background-color: transparent;
}
.btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-primary:not(:disabled):not(.disabled):active, .show>.btn-outline-primary.dropdown-toggle {
    background-color: transparent;
    color: #1b55e2!important;
}
.btn-outline-success:not(:disabled):not(.disabled).active, .btn-outline-success:not(:disabled):not(.disabled):active, .show>.btn-outline-success.dropdown-toggle {
    background-color: transparent;
    color: #8dbf42!important;
}
.btn-outline-info:not(:disabled):not(.disabled).active, .btn-outline-info:not(:disabled):not(.disabled):active, .show>.btn-outline-info.dropdown-toggle {
    background-color: transparent;
    color: #2196f3!important;
}
.btn-outline-danger:not(:disabled):not(.disabled).active, .btn-outline-danger:not(:disabled):not(.disabled):active, .show>.btn-outline-danger.dropdown-toggle {
    background-color: transparent;
    color: #e7515a!important;
}
.btn-outline-warning:not(:disabled):not(.disabled).active, .btn-outline-warning:not(:disabled):not(.disabled):active, .show>.btn-outline-warning.dropdown-toggle {
    background-color: transparent;
    color: #e2a03f!important;
}

.btn.btn-outline-primary .caret {
    border-top-color: #1b55e2;
}
.btn.btn-outline-success .caret {
    border-top-color: #8dbf42;
}
.btn.btn-outline-info .caret {
    border-top-color: #2196f3;
}
.btn.btn-outline-danger .caret {
    border-top-color: #e7515a;
}
.btn.btn-outline-warning .caret {
    border-top-color: #e2a03f;
}