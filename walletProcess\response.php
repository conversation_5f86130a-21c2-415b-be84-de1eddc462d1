<?php
session_start();
include('../admin/lib/db_connection.php');
include('../admin/lib/get_functions.php');
include('../lib/auth.php');
date_default_timezone_set("Asia/Kolkata");
$today_date=date('Y-m-d H:i:s');

$postdata = $_POST;
if($postdata['txnid']==""){
	echo '<script>window.location.href="../add_money_to_wallet.php";</script>';
}else{
$msg = '';
if (isset($postdata ['key'])) {
	$key				=   $postdata['key'];
	$salt				=   '4Ov8BGqEoY';
	$txnid 				= 	$postdata['txnid'];
    $amount      		= 	$postdata['amount'];
	$productInfo  		= 	$postdata['productinfo'];
	$firstname    		= 	$postdata['firstname'];
	$email        		=	$postdata['email'];
	$udf5				=   $postdata['udf5'];
	$mihpayid			=	$postdata['mihpayid'];
	$status				= 	$postdata['status'];
	$resphash				= 	$postdata['hash'];
	//Calculate response hash to verify	
	$keyString 	  		=  	$key.'|'.$txnid.'|'.$amount.'|'.$productInfo.'|'.$firstname.'|'.$email.'|||||'.$udf5.'|||||';
	$keyArray 	  		= 	explode("|",$keyString);
	$reverseKeyArray 	= 	array_reverse($keyArray);
	$reverseKeyString	=	implode("|",$reverseKeyArray);
	$CalcHashString 	= 	strtolower(hash('sha512', $salt.'|'.$status.'|'.$reverseKeyString));
	
	
	if ($status == 'success'  && $resphash == $CalcHashString) {
		$msg = "Transaction Successful...Please do not hit back or refresh button. You will automatically redirect to your wallet page.";
		    $sel=dbQuery("SELECT * FROM tabl_wallet WHERE customer_id='".$_SESSION['customer_id']."'");
			   $num=dbNumRows($sel);
		        if($num>0){
			 dbQuery("UPDATE tabl_wallet SET amount=amount+'".$amount."' WHERE customer_id='".$_SESSION['customer_id']."'");
			}else{
				dbQuery("INSERT INTO tabl_wallet SET amount='".$amount."',customer_id='".$_SESSION['customer_id']."'");
				}	
				dbQuery("INSERT INTO tabl_wallet_details SET customer_id='".$_SESSION['customer_id']."',added_amount='".$amount."',	transaction_id='".$txnid."',date_added='".$today_date."'");	
	   }
	else {
		$msg = "Transaction Failed...Please do not hit back or refresh button. You will automatically redirect to your wallet page.";
	} 
}
else exit(0);
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>PayUmoney Response</title>
</head>
<style type="text/css">
	.main {
		margin-left:30px;
		font-family:Verdana, Geneva, sans-serif, serif;
	}
	.text {
		float:left;
		width:180px;
	}
	.dv {
		margin-bottom:5px;
	}
</style>
<body>
<div class="main">    
    <div class="dv">   
    <span><?php echo $msg; ?></span>
    </div>
</div>
</body>
</html>
<script>
window.setTimeout(function() {
    window.location.href = '../wallet.php';
}, 5000);   
</script>

<?php } ?>
	