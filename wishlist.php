<?php 
session_start();
include('admin/lib/db_connection.php');
include('admin/lib/get_functions.php');
include('lib/auth.php');
$page=1;
$sub_page=0;
date_default_timezone_set("Asia/Kolkata");
$date=date('Y-m-d');
if(isset($_POST['remove']))
{
    dbQuery("DELETE FROM `tabl_wishlist` WHERE id=".$_POST['remove']);
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>Deva King 777</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">

    <!-- <link rel="stylesheet" href="xx.jpg"> -->
    <link rel="icon" href="./assets/img/logo/icon.png">


    <!--Google font-->
    <link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Raleway&amp;display=swap" rel="stylesheet">


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">


    <!--icon css-->
    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
    <link rel="stylesheet" type="text/css" href="assets/css/themify.css">

    <!--Slick slider css-->
    <link rel="stylesheet" type="text/css" href="assets/css/slick.css">
    <link rel="stylesheet" type="text/css" href="assets/css/slick-theme.css">

    <!--Animate css-->
    <link rel="stylesheet" type="text/css" href="assets/css/animate.css">
    <!-- Bootstrap css -->
    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.css">

    <!-- Theme css -->
    <link rel="stylesheet" type="text/css" href="assets/css/color2.css" media="screen" id="color">
    <!-- custom responsive css -->
    <link rel="stylesheet" type="text/css" href="assets/css/responsive.css" media="screen" id="color">
    <style>
    .header-bar .input-group input {
        box-shadow: none !important;
        border: none !important;
    }

    .input-group {
        border: 1px solid gray !important;
    }

    .input-group-text {
        background: none !important;
    }

    @media screen and(max-width : 767px) {
        .header-bar {
            margin-top: 10px;
        }
    }

    .reviews {
        background-color: #f2f0ce;
    }

    .reviews h4 {
        font-size: 25px;

    }

    .reviews i {
        color: #fbc02d !important;
        text-align: center;
    }

    .reviews .carousel-item img {
        width: 500px !important;
    }

    .silder-x {
        display: flex !important;
        justify-content: center !important;
    }

    .scroll-1::-webkit-scrollbar {
        display: none;
    }

    .slider_container {
        position: relative;
        width: 100%;
        min-width: 50rem;
        height: 30rem;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        overflow: hidden;
    }

    .slider {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        animation: 30s cubic-bezier(1, 0.95, 0.565, 1) sliding infinite;
    }

    .slide {
        position: relative;
        min-width: 100%;
        height: 100%;
    }

    .slide img {
        width: 100%;
        height: 100%;
    }

    .slide .caption {
        position: absolute;
        left: 0;
        bottom: 5%;
        font-size: 5rem;
        font-weight: 600;
        color: white;
        text-transform: capitalize;
        background: rgba(0, 0, 0, 0.348);
        backdrop-filter: blur(10px);
        padding: 1rem 5rem;
        border-radius: 0 2rem 2rem 0;
    }

    .slide.one {
        background: rgb(182, 19, 109);
        width: 100% !important;
    }

    .slide.two {
        background: rgb(255, 64, 64);
        width: 100% !important;
    }

    .slide.three {
        background: rgb(11, 173, 188);
        width: 100% !important;
    }

    .slide.four {
        background: rgb(11, 188, 14);
        width: 100% !important;
    }

    .slide.five {
        background: rgb(173, 11, 188);
        width: 100% !important;
    }

    @keyframes sliding {
        0% {
            transform: translateX(0%);
        }

        20% {
            transform: translateX(0%);
        }

        25% {
            transform: translateX(-100%);
        }

        45% {
            transform: translateX(-100%);
        }

        50% {
            transform: translateX(-200%);
        }

        70% {
            transform: translateX(-200%);
        }

        75% {
            transform: translateX(-300%);
        }

        95% {
            transform: translateX(-300%);
        }

        100% {
            transform: translateX(-400%);
        }
    }

    /* P */

    .category-header {
        display: flex;
        gap: 28px;
        /* padding: 30px; */
        padding: 10px 10px 10px 10px;
        justify-content: space-between !important;
        width: 100%;
        background-color: #ffffffff;
        /* overflow: hidden; */
    }


    .category-items img {
        border: 1px solid #832729;
        width: 113px;
        height: 113px;
        border-radius: 50%;
    }

    .category-items img:hover {
        border: 4px dotted #832729;
    }

    .scroll-1 {
        overflow: scroll;

    }

    .scroll-1::-webkit-scrollbar {
        /* display: none; */
    }

    /* @media screen and(max-width : 767px){

} */
    .fist-slider .carousel-caption {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
    }

    @media only screen and (max-width: 600px) {
        .fist-slider .carousel-caption {
            height: 75%;
        }
    }

    @media only screen and (max-width: 600px) {
        .category-items img {
            border: 1px solid #832729;
            width: 65px;
            height: 65px;

        }

        .category-header {
            padding: 9px 0px 9px 14px;
            gap: 19px;
        }

        /* .category-items h6{
    font-size: 15px;
} */
        .centered {
            left: 38% !important;
        }
    }

    .centered {
        color: #ffffff !important;
        color: #ffffff !important;
        font-size: 17px;
        font-weight: 00;
        font-style: italic;
    }

    .category-items h6 {
        font-size: 15px;
        color: gray;
        padding: 10px 0 0 0;
    }

    /* P */
    .silder-h {
        background-color: rgb(255 255 255 / 87%);
        color: #832729;
        padding: 5px;
        border-radius: 5px;
    }

    .shop-btn {
        border: 1px solid #ffffff;
        font-size: 17px;
        /* padding: 5px; */
        border-radius: 5px;
        margin-top: 10px;
    }

    /* .layout-header2 .main-menu-block .input-block .input-box .big-deal-form .input-group .form-control {
        border-radius: 0px 10px 10px 0px;
        border: none;
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
    }

    .layout-header2 .main-menu-block .input-block .input-box .big-deal-form {
        width: 100%;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 10px;
    }

    .layout-header2 .main-menu-block .cart-block {
        margin-left: 18px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
    } */

    .img-wrapperr1 img {
        margin: 0 auto;
        border-radius: 50%;
        border: solid #ff708a;
    }

    .img-wrapperr1 img:hover {
        margin: 0 auto;
        border-radius: 50%;
        border: dotted #832729;
        /* transform: rotate(); */
    }

    @media screen and (max-width: 767px) {
        a.btn.btn-rounded {
            font-size: 9px !important;
        }
    }

    .img-wrapperr1 img {
        margin: 0 auto;
        border-radius: 50%;
        border: solid #832729;
    }

    .services {
        background-color: #F2E9E9;
        color: #832729 !important;
    }

    .btn-rounded {
        font-size: 13px;
        padding: 10px 18px;
        font-weight: 700;
        color: #fff !important;
        /* background-color: #cabdb4; */
        background-color: #832729 !important;
    }

    .cat-1 {
        /* border: 1px solid; */
        display: flex;
        justify-content: center;
        gap: 10px;
        min-width: 100%;
        /* overflow: scroll; */
    }

    .img-wrapperr1 img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
    }

    @media screen and (max-width: 767px) {
        .img-wrapperr1 img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
        }
    }

    .custom-container-2 {
        position: relative;
    }

    .custom-container-2 a {
        text-decoration: none;
        color: black;
    }

    .view-all {
        position: absolute;
        bottom: -163px;
        width: 100%;
        color: black;
        padding: 10px;
        font-size: 20px;
        font-weight: 600;
        text-align: center;
        background-color: #ff708a;
        display: none;
        transition: transform .6s;
    }

    .custom-container-2 .col {
        overflow: unset;
    }

    .custom-container-2:hover .view-all {
        display: block;
        -ms-transform: scale(0.1);
        /* IE 9 */
        -webkit-transform: scale(0.1);
        /* Safari 3-8 */
        transform: scale(1.1);
    }

    @media only screen and (min-width: 320px) and (max-width: 440px) {
        .banner_promt {
            background: none !important;
            padding-top: 16px;
        }

        .title1 {
            text-align: center;
            padding: 5px;
        }
    }

    .custom-container .col {
        overflow: hidden;
    }

    .custom-container-2 img {
        transition: transform .6s;
    }

    .custom-container-2 img:hover {
        -ms-transform: scale(0.1);
        /* IE 9 */
        -webkit-transform: scale(0.1);
        /* Safari 3-8 */
        transform: scale(1.1);
    }

    .btnp {
        border-radius: 10px;
    }


    .scroll-1 {
        /* border: 1px solid; */
        /* overflow-y: scroll; */
        width: 100%;
    }

    /* Hide scrollbar for Chrome, Safari and Opera */
    .example::-webkit-scrollbar {
        display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .example {
        -ms-overflow-style: none;
        /* IE and Edge */
        scrollbar-width: none;
        /* Firefox */
    }

    .myimg {
        width: 113px;
        height: 113px;
    }

    .myimg img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    .banimg {
        height: 440px;
        width: 100%;
    }

    .banimg img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    @media only screen and (max-width: 600px) {
        .myimg {
            width: 65px;
            height: 65px;
        }

        .banimg {
            height: 211px;
            width: 100%;
        }

        .layout-header2 {
            background-color: #f8f7f3 !important;
            /* background-color: #D7AF8B !important; */
            padding: 10px 0 !important;
            position: fixed;
            width: 100%;
            z-index: 9;
            top: 0;
        }
    }


    .custom-container .col {
        overflow: unset;
    }

    .product .product-box {
        margin-right: 15px;
        margin-top: 15px;
        padding-right: 0px;
    }

    .product .product-box {
        margin-right: 15px;
        margin-top: 15px;
        padding-right: 0px;
    }

    .btn-rounded:hover {
        font-size: 13px;
        padding: 10px 18px;
        font-weight: 700;
        color: #fff !important;
        /* background-color: #cabdb4; */
        background-color: #832729 !important;
    }

    .likeicon {
        position: absolute;
        z-index: 8;
        top: 0;
        right: 0;
        text-align: right;
        padding: 11px;
        color: brown;
        font-size: 20px;
    }
    </style>
</head>

<body class="bg-light ">

    <!-- loader start -->

    <!-- loader end -->

    <!--header start-->
    <?php include('header.php')?>
    <!--header end-->

    <!-- breadcrumb start -->
    <div class="breadcrumb-main ">
        <div class="container">
            <div class="row">
                <div class="col">
                    <div class="breadcrumb-contain">
                        <div>
                            <h2>My Wishlist</h2>
                            <ul>
                                <li><a href="#">home</a></li>
                                <li><i class="fa fa-angle-right"></i></li>
                                <li><a href="#">My wishlist</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- breadcrumb End -->

    <!--section start-->
    <section class="product section-pb-space">
        <div class="custom-container">
            <div class="row">
                <?php
                $wishlistSql = dbQuery("SELECT * FROM tabl_likes WHERE user_id='".$_SESSION['user_id']."'");
                if(mysqli_num_rows($wishlistSql)>0)
                {
                    while($wishlistRow = dbFetchAssoc($wishlistSql))
                    {
                        $trending = dbQuery("SELECT * FROM tabl_products WHERE id=".$wishlistRow['product_id']." AND `status`='1' ");
                        if($res_trending = dbFetchAssoc($trending)){

                            $trending_off = (($res_trending['old_price'] - $res_trending['price']) / $res_trending['old_price']) * 100;
                            $trending_off = round($trending_off, 0);

                            if (isset($_SESSION['user_id'])) {
                                $sel_like = dbQuery("SELECT * FROM tabl_likes WHERE product_id='" . $res_trending['id'] . "' AND user_id = '" . $_SESSION['user_id'] . "'");
                                $num_like = dbNumRows($sel_like);

                                $is_liked = $num_like > 0 ? "fa-solid" : "fa-regular";
                                // $is_liked_text = $num_like > 0 ? "Liked" : "Like";
                            } else {
                                $is_liked = 'fa-regular';
                                // $is_liked_text = "fa-regular";
                            }
                            ?>

                            <div class="col-lg-3 col-6 pr-0">
                                <div class="product-box">
                                    <div class="product-imgbox">
                                        <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>">
                                            <div class="product-front">
                                                <img src="assets/images/products/<?php echo $res_trending['image']; ?>" class="img-fluid" alt="product">
                                            </div>
                                        </a>
                                        <!-- <div class="likeicon">
                                            <i class="fa-solid fa-heart"></i>
                                        </div> -->

                                        <div class="likeicon"
                                            onclick="likeProduct(event, <?php echo $res_trending['id']; ?>, 1)">
                                            <i class="<?= $is_liked; ?> fa-heart"
                                                id="like_icon_1_<?= $res_trending['id']; ?>"></i>
                                        </div>
                                    
                                        <div class="share-btn" onclick="copyLinkToClipboard('<?= $res_trending['id']; ?>')">
                                            <i class="fa-solid fa-share"></i>
                                        </div>

                                        <div class="new-label">
                                            <div><?php echo $trending_off; ?>%</div>
                                        </div>
                                    </div>
                                    <div class="product-detail detail-center detail-inverse">
                                        <div class="detail-title">
                                            <div class="detail-right">
                                                <div class="check-price">
                                                    Rs. <?php echo $res_trending['old_price'] ?> </div>
                                                <div class="price">
                                                    <div class="price">
                                                        Rs. <?php echo $res_trending['price'] ?> </div>
                                                </div>
                                            </div>
                                            <div class="detail-left mt-1">

                                                <a href="#" tabindex="-1">
                                                    <h6 class="price-title"><?php echo $res_trending['name'] ?></h6>
                                                </a>
                                            </div>

                                            <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>" class="btn btn-rounded mtb-20">Add To Cart </a>
                                            <a href="product-detail.php?productID=<?php echo $res_trending['id']; ?>" class="btn btn-rounded mtb-20">Buy Now </a>
                                            <!-- <form method="POST">
                                                <button type="submit" name="remove" value="<?php echo $wishlistRow['id']; ?>" class="btn btn-rounded mtb-20">Removed</button>
                                            </form> -->
                                        </div>
                                    </div>
                                </div>

                            </div>
                        <?php
                        }
                    }
                } else {
                    echo 'No Product Added..';
                }
                ?>
                

            </div>

            <div class="row cart-buttons">
                <div class="col-12 text-center">
                    <a href="index.php" class="btn btn-normal">Shop Now</a>

                </div>
            </div>
           
        </div>
    </section>
    <!--section end-->

    <!--footer-start-->

    <script>
        function likeProduct(event, product_id, type) {
            event.stopPropagation();

            const icon = document.getElementById("like_icon_" + type + "_" + product_id);
            const like_text = document.getElementById("like_text");

            // alert("Like ID: like_" + propertyId);

            // Make an AJAX call to the like_dislike.php script
            const xhr = new XMLHttpRequest();
            xhr.open("POST", "./ajax/like_dislike.php", true);
            xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        // Toggle class and change title based on response
                        // btn.classList.toggle("liked", response.isLiked);


                        if (response.isLiked) {
                            // if (icon.classList.contains('fa-regular')) {
                            icon.classList.remove('fa-regular');
                            icon.classList.add('fa-solid');
                        } else {
                            icon.classList.remove('fa-solid');
                            icon.classList.add('fa-regular');
                            // }
                        }

                        window.location.reload();

                        // like_text.innerHTML = response.isLiked ? "Liked" : "Like";
                    } else {
                        console.error("An error occurred: " + response.message);
                        alert(response.message);
                    }
                }
            };
            xhr.send("product_id=" + product_id);

        }


        function copyLinkToClipboard(pro_id) {
        // Create a temporary textarea element to hold the link
        const textarea = document.createElement('textarea');
        textarea.value = 'https://devaking777.com/product-detail.php?productID=' + pro_id;
        document.body.appendChild(textarea);

        // Select the text in the textarea
        textarea.select();
        textarea.setSelectionRange(0, 99999); // For mobile devices

        // Copy the selected text to the clipboard
        try {
            const successful = document.execCommand('copy');
            const message = successful ? 'Link copied to clipboard!' : 'Failed to copy link.';
            console.log(message);
            alert(message);
        } catch (err) {
            console.error('Oops, unable to copy:', err);
        }

        // Remove the temporary textarea element
        document.body.removeChild(textarea);
    }
    </script>



    <?php include('footer.php')?>
    <!--footer-end-->
    <!-- Quick-view modal popup start-->
    <div class="modal fade bd-example-modal-lg theme-modal" id="quick-view" tabindex="-1" role="dialog"
        aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content quick-view-modal">
                <div class="modal-body">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <div class="row">

                        <div class="col-lg-12">
                            <?php $sel_cat=dbQuery("SELECT sub_cat_id FROM tabl_cart WHERE session_id='".$session_id."' Group BY sub_cat_id");
					$res_cat=dbFetchAssoc($sel_cat);

					$sel_offer=dbQuery("SELECT * FROM tabl_promo_code WHERE start_date<='".$date."' AND end_date>='".$date."' AND FIND_IN_SET(".$res_cat['sub_cat_id'].",sub_cat_id)");
					   $offer_num=dbNumRows($sel_offer);
							if($offer_num>0){?>
                            <div class="product-right">
                                <form name="promo-form" id="promo-form" method="post">
                                    <div class="theme-card">
                                        <h3 class="text-center">Available Coupons</h3>
                                        <div class="offers_pdp">
                                            <div class="head_div">
                                                <ul>
                                                    <?php while($res_offer=dbFetchAssoc($sel_offer)){
						
                                                    $sel1=dbQuery("SELECT promo_Code FROM `tabl_cart_promo` WHERE session_id='".$session_id."' AND promo_code='".$res_offer['id']."'");
                                                    $res1=dbFetchAssoc($sel1);

                                                    if($res_offer['id']== $res1['promo_Code']){
                                                        $checked='checked';
                                                    }else{
                                                        $checked='';
                                                    }
                                                    ?>

                                                    <li class="pdp_offrs mtb-20" id="first_promo">
                                                        <input type="checkbox" name="promo_code[]"
                                                            value="<?php echo $res_offer['id'];?>" class="mr-10"
                                                            <?php echo $checked;?>>
                                                        <div class="coupons_code">
                                                            <span><?php echo $res_offer['name'];?></span>
                                                        </div>

                                                        <div>
                                                            <p><?php echo $res_offer['description'];?></p>
                                                            <span><a href="term.php">T&amp;C</a>
                                                            </span>
                                                        </div>
                                                    </li>
                                                    <?php } ?>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="product-buttons text-center">
                                        <div class="promo_loader" style="text-align: center;text-transform: none;">
                                        </div>
                                        <button type="submit" id="submit" class="btn btn-normal">Apply Coupon</button>
                                    </div>

                                </form>
                            </div>
                            <?php }else{?>
                            <div class="product-right">
                                <div class="theme-card">
                                    <h4 class="text-center">No Coupon Available!</h4>
                                </div>
                            </div>
                            <?php }?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Quick-view modal popup end-->




    <!-- tap to top -->
    <div class="tap-top">
        <div>
            <i class="fa fa-angle-double-up"></i>
        </div>
    </div>
    <!-- tap to top End -->


    <!-- latest jquery-->
    <script src="assets/js/jquery-3.3.1.min.js"></script>

    <!-- menu js-->
    <script src="assets/js/menu.js"></script>

    <!-- popper js-->
    <script src="assets/js/popper.min.js"></script>

    <!-- slick js-->
    <script src="assets/js/slick.js"></script>

    <!-- Bootstrap js-->
    <script src="assets/js/bootstrap.js"></script>

    <!-- Theme js-->
    <script src="assets/js/script.js"></script>


    <script>
        $("#promo-form").submit(function(e) {
            var values = [];
            $("input[name='promo_code[]']:checked").each(function() {
                values.push($(this).val());
            });
            if (values != "") {
                $("#submit").attr('disabled', true);
                $(".promo_loader").html(
                    '<img src="loader.gif" style="width: 20px;"></i> please wait while we apply coupon code...');

                $.ajax({
                    url: 'ajax/apply_promo_codes.php',
                    type: 'post',
                    data: {
                        'values': values
                    },
                    success: function(data) {
                        if (data == 1) {
                            $("#submit").attr('disabled', false);
                            $(".promo_loader").html(
                                '<span style="color: #59a759;font-weight: 500;"><i class="fa fa-check" style="color: #59a759;"></i> Promo code apply successfully!</span>'
                                );
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            $("#submit").attr('disabled', false);
                            $(".promo_loader").html(
                                '<span style="color: #e44d14;font-weight: 500;"><i class="fa fa-trash-o" style="color: #e44d14;"></i> something is wrong!</span>'
                                );
                        }
                    },
                });
            } else {
                alert("Please select promo code!!!");
            }
            //$("#submit").attr('disabled',true);
            e.preventDefault(); // avoid to execute the actual submit of the form.
        });
    </script>

<script>
function remove_cart(cart_id) {
    $(".remove_cart").html('<img src="loader.gif" style="width: 10px;"></i> please wait...');
    $(".icon-remove").html('');
    $.ajax({
        url: 'ajax/remove_cart.php',
        type: 'post',
        data: {
            'cart_id': cart_id
        },
        success: function(data) {
            if (data == 1) {
                location.reload();
            }
        },
    });
}
</script>
<script>
function update_cart_qty(type, row_id, cart_id) {
    if (type == 1) {
        var qty = $("#qty_" + row_id).val();
        var new_qty = parseInt(qty) + 1;
        $("#qty_" + row_id).val(new_qty);
    } else {
        var qty = $("#qty_" + row_id).val();
        if (qty != 1) {
            var new_qty = parseInt(qty) - 1;
            $("#qty_" + row_id).val(new_qty);
        }
    }
    $(".qty_loader_" + row_id).html('<img src="loader.gif" style="width: 10px;"></i> please wait...');
    $(".cart_button_" + row_id).attr('disabled', true);

    $.ajax({
        url: 'ajax/update_cart_qty.php',
        type: 'post',
        data: {
            'new_qty': new_qty,
            'cart_id': cart_id
        },
        success: function(data) {
            if (data == 1) {
                location.reload();
            }
        },
    });

}
</script>





</body>
<style>
    @media screen and(max-width : 767px) {
        .footer-brand h5 {
            font-size: 18px;
        }
    }

    .title1 {
        text-align: center;
    }

    .wedding-padding1 {
        padding-right: 0.1rch;
        /* margin: 0 0 0 0 ; */
    }

    .wedding-padding2 {
        padding-left: 0.1rch;
    }

    .lg-img img {
        width: 100%;
        border-radius: 5px;
    }

    .lg-img {
        width: 100%;
        border-radius: 5px;
        margin-bottom: 5px;
        margin-top: 5px;
    }

    .wedding-md-img {
        width: 100%;
        position: relative;
        text-align: center;
    }

    /* @media screen and(max-width: 767px){
            .wedding-md-img img{
            width: 100%;
            border-radius: 5px;
            height: 200px;
        }
        } */
    .wedding-md-img img {
        width: 100%;
        border-radius: 5px;
        /* height: 284px; */
    }

    .centered {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .product .product-box:hover .product-detail.detail-center .detail-title {
        opacity: 1!important;
    }

    .detail-title:hover {
        opacity: 1!important;
    }
    .product-detail:hover {
        opacity: 1!important;
    }


    .section-big-pt-space {
        padding-top: 0px;
    }

    .mt-50 {
        margin-top: 50px;
    }

    .mtb-20 {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .btn-rounded {
        font-size: 13px;
        padding: 10px 18px;
        font-weight: 700;
        color: #fff !important;
        /* background-color: #cabdb4;*/
        background-color: #ff708a;
        border-radius: 25px;
        position: relative;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
        line-height: 1;
        display: inline-block;
        letter-spacing: 0.05em;
        font-style: italic;
    }

    .btn-rounded:hover {
        background-color: #ff708a;
    }


    .instagram-box img {
        max-width: 145px;
        height: 145px;
    }


    .product .product-box {
        margin-right: 15px;
        margin-top: 15px;
        padding-right: 0px;
    }

    .no-arrow .slick-next,
    .no-arrow .slick-prev {
        display: none !important;
    }
</style>

</html>
    